# 系统休眠条件分析报告

## 🎯 休眠机制概述

系统设计了智能休眠机制，当连续遇到失败时自动进入休眠状态，避免无效的重复操作，保护系统资源。

## 📊 休眠触发条件

### 核心触发条件
```javascript
let consecutiveFailures = 0;
const maxConsecutiveFailures = 3;  // 连续失败3次
const sleepTime = 30;              // 休眠30分钟

if (consecutiveFailures >= maxConsecutiveFailures) {
  Statistics.setSleepMode(sleepTime);
}
```

### 触发场景

#### 1. 正常执行失败
```javascript
const success = await performSingleFollow();
if (success) {
  consecutiveFailures = 0;  // 成功时重置计数器
} else {
  consecutiveFailures++;    // 失败时累加
  if (consecutiveFailures >= 3) {
    // 进入休眠
  }
}
```

#### 2. 异常捕获失败
```javascript
try {
  // 执行跟进操作
} catch (error) {
  consecutiveFailures++;    // 异常也算失败
  if (consecutiveFailures >= 3) {
    // 进入休眠
  }
}
```

## 🔍 导致失败的具体情况

### 1. 跟进按钮未找到
```javascript
const followButton = await findFollowButton();
if (!followButton) {
  Statistics.addFailure(true);
  Statistics.addLog('❌ 未找到跟进按钮，等待10秒后重试', 'error');
  return false;  // 返回失败
}
```

**触发条件**：
- 页面上没有"跟进"按钮
- 按钮被禁用（disabled）
- 按钮被隐藏（display: none）
- 页面还未加载完成

### 2. 文本输入框处理失败
```javascript
const result = await inputText(textInput);
if (result) {
  // 成功处理
} else {
  Statistics.addFailure();
  Statistics.addLog('❌ 跟进操作失败', 'error');
  return false;  // 返回失败
}
```

**触发条件**：
- 文本输入框未找到
- 表单填充失败
- 保存操作失败
- 弹窗关闭失败

### 3. 系统异常
```javascript
try {
  // 各种操作
} catch (error) {
  Statistics.addFailure();
  Statistics.addLog(`❌ ${error.message}`, 'error');
  return false;  // 返回失败
}
```

**触发条件**：
- JavaScript运行时错误
- DOM操作异常
- 网络请求失败
- 其他未预期的错误

## 📈 失败计数逻辑

### 计数器管理
```javascript
let consecutiveFailures = 0;

// 成功时重置
if (success) {
  consecutiveFailures = 0;
}

// 失败时累加
else {
  consecutiveFailures++;
}
```

### 重置条件
- ✅ **任何一次成功操作**：立即重置为0
- ✅ **手动停止后重启**：重新开始计数
- ✅ **休眠结束后**：重置为0重新开始

### 累加条件
- ❌ **跟进按钮未找到**
- ❌ **文本输入处理失败**
- ❌ **系统异常错误**
- ❌ **任何导致 `performSingleFollow()` 返回 `false` 的情况**

## 🕐 休眠时间设置

### 固定休眠时间
```javascript
const sleepTime = 30; // 30分钟
```

### 休眠期间行为
```javascript
while (isRunning && Statistics.isSleeping && Date.now() - sleepStartTime < sleepTime * 60000) {
  await wait(1000); // 每秒检查一次
}
```

**休眠期间**：
- 🔄 每秒检查一次状态
- ⏰ 显示剩余休眠时间
- 🛑 可以手动停止
- 📊 更新界面状态

## 🎯 典型休眠场景

### 场景1：页面数据加载问题
```
第1次：❌ 未找到跟进按钮，等待10秒后重试
第2次：❌ 未找到跟进按钮，等待10秒后重试  
第3次：❌ 未找到跟进按钮，等待10秒后重试
💤 进入休眠模式，30分钟后自动继续
```

### 场景2：网络或系统问题
```
第1次：❌ 跟进操作失败
第2次：❌ 跟进操作失败
第3次：❌ 跟进操作失败
💤 进入休眠模式，30分钟后自动继续
```

### 场景3：表单处理问题
```
第1次：❌ 表单填充失败
第2次：❌ 保存操作失败
第3次：❌ 弹窗关闭失败
💤 进入休眠模式，30分钟后自动继续
```

### 场景4：混合失败
```
第1次：❌ 未找到跟进按钮
第2次：✅ 操作成功 (计数器重置为0)
第1次：❌ 表单填充失败
第2次：❌ 保存失败
第3次：❌ 系统异常
💤 进入休眠模式，30分钟后自动继续
```

## 🔧 休眠状态管理

### 进入休眠
```javascript
Statistics.setSleepMode(sleepTime);
// 设置休眠状态
// 设置结束时间
// 启动倒计时
// 更新界面显示
```

### 休眠期间
```javascript
// 状态检查
this.isSleeping = true
this.sleepEndTime = Date.now() + minutes * 60000

// 界面更新
this.startSleepTimer()
this.updatePopup()

// 日志记录
this.addLog(`💤 进入休眠模式，${minutes}分钟后自动继续`, 'info')
```

### 退出休眠
```javascript
// 自动退出（时间到）
if (remainingMs <= 0) {
  this.clearSleepMode();
  this.addLog('休眠结束，继续运行', 'info');
}

// 手动退出（停止运行）
if (!isRunning) {
  Statistics.clearSleepMode();
}
```

## 📊 休眠的作用

### 1. 保护系统资源
- 避免无效的重复操作
- 减少服务器压力
- 防止被系统检测为异常行为

### 2. 等待问题解决
- 给页面加载更多时间
- 等待网络问题恢复
- 等待系统维护完成

### 3. 智能恢复
- 30分钟后自动重试
- 重置失败计数器
- 从当前位置继续

## 🎊 总结

### 休眠触发的核心条件
1. ✅ **连续失败3次**：任何导致 `performSingleFollow()` 返回 `false` 的情况
2. ✅ **失败类型包括**：
   - 跟进按钮未找到
   - 文本输入处理失败
   - 系统异常错误
3. ✅ **休眠时间**：固定30分钟
4. ✅ **重置条件**：任何一次成功操作

### 设计优势
- 🛡️ **智能保护**：避免无效重复操作
- 🔄 **自动恢复**：30分钟后自动继续
- 📊 **状态透明**：清晰的休眠状态显示
- 🛑 **可控制**：支持手动停止

这个休眠机制确保了系统在遇到持续问题时能够智能地暂停，等待问题解决后自动恢复，大大提升了系统的稳定性和可靠性！
