# 线索等级随机选择逻辑修复说明

## 问题描述

您发现了一个重要的逻辑问题：即使没有勾选"随机选择线索等级"，程序也可能进行随机选取或强制修改已有值。

## 原有问题分析

### 1. 检测逻辑问题
```javascript
// 原有逻辑（有问题）
if (field.name === '线索等级') {
  if (settings && settings.randomLevelSelect) {
    // 随机选择模式
  } else {
    // 原有逻辑：如果不是30天就强制修改
    const isNot30Days = !value.includes('30天内跟进');
    if (isEmpty || isNot30Days) {
      fieldsToProcess.push(field);  // 问题：会强制修改已有值
    }
  }
}
```

### 2. 配置问题
```javascript
// 字段配置中硬编码了随机选择
{
  name: '线索等级',
  randomSelect: true  // 问题：硬编码启用
}
```

### 3. 处理逻辑问题
```javascript
// 处理时会检查两个条件
const enableRandomSelect = (fieldName === '线索等级' && 
                           ((fieldConfig && fieldConfig.randomSelect) ||  // 硬编码true
                            (settings && settings.randomLevelSelect)));   // 用户设置
```

## 修复内容

### 1. 修复检测逻辑
```javascript
// 修复后的逻辑
if (field.name === '线索等级') {
  if (settings && settings.randomLevelSelect) {
    // 启用随机选择：总是处理该字段
    fieldsToProcess.push(field);
    Statistics.addLog(`🎲 线索等级启用随机选择模式，当前值: "${value}"`);
  } else {
    // 未启用随机选择：只处理空值
    if (isEmpty) {
      fieldsToProcess.push(field);
      Statistics.addLog(`🔍 线索等级为空，将设置默认值`);
    } else {
      Statistics.addLog(`🔍 线索等级已有值"${value}"，跳过处理`);
    }
  }
}
```

### 2. 移除硬编码配置
```javascript
// 修复后的字段配置
{
  name: '线索等级',
  path: "label[for='level']",
  type: 'select',
  options: ['B（30天内跟进）', 'A（7天内跟进）', 'H（2天内跟进）']
  // 移除了 randomSelect: true
}
```

### 3. 简化处理逻辑
```javascript
// 修复后的处理逻辑
const enableRandomSelect = (fieldName === '线索等级' && settings && settings.randomLevelSelect);
// 只依赖用户设置，不再检查字段配置
```

## 修复后的行为

### 1. 勾选"随机选择线索等级"时
- ✅ 总是处理线索等级字段
- ✅ 即使已有值也会重新随机选择
- ✅ 日志显示：`🎲 线索等级启用随机选择模式，当前值: "xxx"`
- ✅ 从H、A、B中随机选择

### 2. 未勾选"随机选择线索等级"时
- ✅ 只检测线索等级是否为空
- ✅ 有值时跳过处理，日志显示：`🔍 线索等级已有值"xxx"，跳过处理`
- ✅ 无值时设置默认值，日志显示：`🔍 线索等级为空，将设置默认值`
- ✅ 不会强制修改已有的有效值

## 测试场景

### 场景1：未勾选随机选择 + 已有值
- **当前值**：`A（7天内跟进）`
- **预期行为**：跳过处理，保持原值
- **日志**：`🔍 线索等级已有值"A（7天内跟进）"，跳过处理`

### 场景2：未勾选随机选择 + 无值
- **当前值**：空
- **预期行为**：设置默认值（按预期选项顺序）
- **日志**：`🔍 线索等级为空，将设置默认值`

### 场景3：勾选随机选择 + 已有值
- **当前值**：`B（30天内跟进）`
- **预期行为**：重新随机选择
- **日志**：`🎲 线索等级启用随机选择模式，当前值: "B（30天内跟进）"`

### 场景4：勾选随机选择 + 无值
- **当前值**：空
- **预期行为**：随机选择
- **日志**：`🎲 线索等级启用随机选择模式，当前值: ""`

## 优势

1. **逻辑清晰**：勾选随机选择才随机，否则只处理空值
2. **用户友好**：不会意外修改用户已设置的值
3. **行为可预测**：根据用户设置明确执行对应逻辑
4. **日志详细**：清楚显示处理原因和结果

现在线索等级的处理逻辑完全符合您的需求：只有勾选了随机选择才会进行随机选取，否则只在字段为空时才处理。
