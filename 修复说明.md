# 线索等级随机选择功能修复说明

## 问题分析

从日志可以看出，虽然显示了"🎲 线索等级启用随机选择模式"，但实际上并没有执行随机选择逻辑。问题出现在以下几个方面：

### 1. 设置传递问题
- 在 `detectEmptyRequiredFields` 函数中，使用了 `field.randomSelect` 来判断是否启用随机选择
- 但是字段配置中的 `randomSelect` 属性是硬编码的，没有从用户设置中获取

### 2. 函数调用链问题
- 线索等级实际上是通过 `handleSelectByPath` 函数处理的
- 该函数没有接收设置参数，无法知道是否启用了随机选择

### 3. 逻辑判断问题
- 即使启用了随机选择，如果当前值已经是有效值，系统可能认为不需要处理

## 修复内容

### 1. 修改字段检测逻辑
```javascript
// 在 detectEmptyRequiredFields 函数中
if (field.name === '线索等级') {
  // 如果启用随机选择，总是处理该字段
  if (settings && settings.randomLevelSelect) {
    fieldsToProcess.push(field);
    Statistics.addLog(`🎲 线索等级启用随机选择模式，当前值: "${value}"`);
  } else {
    // 原有逻辑...
  }
}
```

### 2. 修改函数签名和调用
```javascript
// 修改函数调用，传递字段配置
success = await handleSelectByPath(field.path, field.name, field.options, field);

// 修改函数签名，接收字段配置
async function handleSelectByPath(cssPath, fieldName, expectedOptions, fieldConfig = null)
```

### 3. 修改选择逻辑
```javascript
// 在 handleSelectByPath 函数中
const enableRandomSelect = (fieldName === '线索等级' && 
                           ((fieldConfig && fieldConfig.randomSelect) || 
                            (settings && settings.randomLevelSelect)));

if (enableRandomSelect && validOptions.length > 0) {
  // 随机选择模式
  const randomIndex = Math.floor(Math.random() * validOptions.length);
  selectedOption = validOptions[randomIndex];
  Statistics.addLog(`🎲 随机选择线索等级 (${randomIndex + 1}/${validOptions.length}): ${selectedOption.textContent.trim()}`);
} else {
  // 原有逻辑...
}
```

## 修复后的工作流程

1. **用户勾选"随机选择线索等级"**
2. **设置保存到 Chrome Storage**
3. **检测字段时**：
   - 如果启用随机选择，总是将线索等级字段加入处理列表
   - 显示日志：`🎲 线索等级启用随机选择模式，当前值: "xxx"`
4. **处理字段时**：
   - 调用 `handleSelectByPath` 并传递字段配置
   - 检测到是线索等级且启用随机选择
   - 从所有有效选项中随机选择一个
   - 显示日志：`🎲 随机选择线索等级 (x/3): 选择的等级`

## 预期效果

修复后，当用户启用"随机选择线索等级"时：
- 每次跟进都会重新随机选择线索等级
- 即使当前已有值，也会被随机替换
- 日志中会显示详细的随机选择信息
- 三个等级（H、A、B）都有相等的被选中概率

## 测试建议

1. **启用随机选择**：勾选"随机选择线索等级"选项
2. **运行多次跟进**：观察日志中是否出现随机选择的信息
3. **检查结果**：确认线索等级确实在H、A、B之间随机变化
4. **禁用测试**：取消勾选，确认恢复原有的优先选择逻辑
