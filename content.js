let isRunning = false;
let settings = null;

// 发送初始化消息
console.log('[自动跟进助手] Content script已加载');
chrome.runtime.sendMessage({
  action: 'contentScriptLoaded',
  message: 'Content script已加载'
});

// 日志和统计管理
const Statistics = {
  totalAttempts: 0,
  successCount: 0,
  failureCount: 0,
  totalSuccess: 0,
  historyTotal: 0,
  buttonFailures: 0,    // 跟进按钮未找到的次数
  isSleeping: false,    // 休眠状态
  sleepEndTime: null,   // 休眠结束时间
  startTime: null,
  logs: [],
  maxLogs: 100000,
  sleepTimer: null,
  skipCurrentRecord: false, // 添加跳过标记
  currentIndex: 0,  // 添加当前位置记录

  start(reset = false) {
    if (reset) {
      this.reset();
    } else {
      this.startTime = new Date();
      this.successCount = 0;
      this.failureCount = 0;
      this.totalAttempts = 0;
      this.buttonFailures = 0;
      this.isSleeping = false;
      this.sleepEndTime = null;
      this.logs = [];
      this.loadHistory();
    }
    this.updatePopup();
    this.addLog('开始自动化流程', 'info');
  },

  loadHistory() {
    const savedTotal = localStorage.getItem('historyTotal');
    const savedSuccess = localStorage.getItem('totalSuccess');
    this.historyTotal = savedTotal ? parseInt(savedTotal) : 0;
    this.totalSuccess = savedSuccess ? parseInt(savedSuccess) : 0;
  },

  saveHistory() {
    localStorage.setItem('historyTotal', this.historyTotal.toString());
    localStorage.setItem('totalSuccess', this.totalSuccess.toString());
  },

  addSuccess() {
    this.successCount++;
    this.totalSuccess++;  // 增加历史成功数
    this.historyTotal++;  // 增加历史总数
    this.saveHistory();   // 保存历史数据
    this.updatePopup();   // 更新显示
    this.addLog('✅ 操作成功', 'success');
  },

  addFailure(isButtonFailure = false) {
    this.failureCount++;
    if (isButtonFailure) {
      this.buttonFailures++;
    }
    this.historyTotal++;  // 增加历史总数
    this.saveHistory();   // 保存历史数据
    this.updatePopup();   // 更新显示
    this.addLog('❌ 操作失败', 'error');
  },

  addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });

    // 根据类型添加不同的前缀
    let prefix = '';
    switch(type) {
      case 'success': prefix = '✅ '; break;
      case 'error': prefix = '❌ '; break;
      case 'warning': prefix = '⚠️ '; break;
      case 'info': prefix = 'ℹ️ '; break;
    }

    const logMessage = `${prefix}${message}`;
    
    // 发送日志到popup
    try {
      chrome.runtime.sendMessage({
        action: 'log',
        data: {
          timestamp,
          message: logMessage,
          type
        }
      });
    } catch (error) {
      console.error('发送日志失败:', error);
    }

    // 保存到本地日志
    this.logs.unshift({
      time: timestamp,
      message: logMessage,
      type: type
    });

    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }

    // 保存日志到storage
    chrome.storage.local.get(['savedLogs'], (data) => {
      const logs = data.savedLogs || [];
      logs.unshift({
        time: timestamp,
        message: logMessage,
        type: type
      });

      if (logs.length > 100) {
        logs.pop();
      }

      chrome.storage.local.set({ savedLogs: logs });
    });

    console.log(`[自动跟进助手] ${timestamp} ${logMessage}`);
    this.updatePopup();
  },

  updatePopup() {
    // 发送完整的统计数据到 popup
    try {
      chrome.runtime.sendMessage({
        action: 'updateStats',
        stats: {
          ...this.getStats(),
          currentSuccess: this.successCount,
          currentFail: this.failureCount,
          totalSuccess: this.totalSuccess,
          historyTotal: this.historyTotal
        }
      });
    } catch (error) {
      console.error('发送统计更新失败:', error);
    }
  },

  getStats() {
    return {
      successCount: this.successCount,
      failureCount: this.failureCount,
      buttonFailures: this.buttonFailures,
      totalSuccess: this.totalSuccess,
      historyTotal: this.historyTotal,
      logs: this.logs,
      status: {
        isRunning,
        isSleeping: this.isSleeping,
        sleepEndTime: this.sleepEndTime
      }
    };
  },

  startSleepTimer() {
    if (this.sleepTimer) {
      clearInterval(this.sleepTimer);
    }

    this.sleepTimer = setInterval(() => {
      // 如果已停止运行，立即清除定时器
      if (!isRunning || !this.isSleeping || !this.sleepEndTime) {
        clearInterval(this.sleepTimer);
        this.sleepTimer = null;
        this.isSleeping = false;
        this.sleepEndTime = null;
        this.updatePopup();
        return;
      }

      // 计算剩余时间（精确到秒）
      const now = new Date();
      const remainingMs = new Date(this.sleepEndTime) - now;
      const remainingMinutes = Math.floor(remainingMs / 60000);
      const remainingSeconds = Math.floor((remainingMs % 60000) / 1000);
      
      // 格式化时间显示 (确保分钟和秒数都是两位数)
      const timeDisplay = `${String(remainingMinutes).padStart(2, '0')}分${String(remainingSeconds).padStart(2, '0')}秒`;

      // 发送实时更新消息
      chrome.runtime.sendMessage({
        action: 'updateSleepStatus',
        data: {
          isSleeping: true,
          timeDisplay,
          remainingMs,
          sleepEndTime: this.sleepEndTime,
          isRunning: isRunning
        }
      });

      if (remainingMs <= 0) {
        this.clearSleepMode();
        this.addLog('休眠结束，继续运行', 'info');
      }

      this.updatePopup();
    }, 1000);
  },

  setSleepMode(minutes) {
    this.isSleeping = true;
    this.sleepEndTime = new Date(Date.now() + minutes * 60 * 1000);
    
    // 立即发送一次状态更新
    const remainingMs = this.sleepEndTime - new Date();
    const remainingMinutes = Math.floor(remainingMs / 60000);
    const remainingSeconds = Math.floor((remainingMs % 60000) / 1000);
    const timeDisplay = `${String(remainingMinutes).padStart(2, '0')}分${String(remainingSeconds).padStart(2, '0')}秒`;

    chrome.runtime.sendMessage({
      action: 'updateSleepStatus',
      data: {
        isSleeping: true,
        timeDisplay,
        remainingMs,
        sleepEndTime: this.sleepEndTime,
        isRunning: isRunning
      }
    });
    
    this.startSleepTimer();
    this.updatePopup();
    this.addLog(`💤 进入休眠模式，${minutes}分钟后自动继续`, 'info');
  },

  clearSleepMode() {
    // 先清除定时器
    if (this.sleepTimer) {
      clearInterval(this.sleepTimer);
      this.sleepTimer = null;
    }

    this.isSleeping = false;
    this.sleepEndTime = null;

    // 发送休眠结束状态
    chrome.runtime.sendMessage({
      action: 'updateSleepStatus',
      data: {
        isSleeping: false,
        timeDisplay: '',
        remainingMs: 0,
        isRunning: isRunning // 使用当前运行状态
      }
    });
    
    this.updatePopup();
  },

  // 修改复位方法
  reset() {
    // 直接复位所有数据
    this.successCount = 0;
    this.failureCount = 0;
    this.totalAttempts = 0;
    this.buttonFailures = 0;
    this.isSleeping = false;
    this.sleepEndTime = null;
    this.logs = [];
    this.totalSuccess = 0;
    this.historyTotal = 0;
    this.currentIndex = 0;

    // 清除本地存储
    localStorage.removeItem('historyTotal');
    localStorage.removeItem('totalSuccess');

    // 更新显示
    this.updatePopup();
    this.addLog('🔄 统计数据已复位', 'info');

    // 发送复位消息到 popup
    try {
      chrome.runtime.sendMessage({
        action: 'statsReset',
        stats: this.getStats()
      });
    } catch (error) {
      console.error('发送复位消息失败:', error);
    }
  },

  moveToNext() {
    this.currentIndex++;
    this.addLog(`📍 移动到第 ${this.currentIndex + 1} 条记录`);
  },
};

// 修改查找跟进按钮的函数
async function findFollowButton() {
  try {
    // 获取所有跟进按钮
    const buttons = Array.from(document.querySelectorAll('button')).filter(btn => 
      btn.textContent.includes('跟进') && !btn.disabled && 
      window.getComputedStyle(btn).display !== 'none'
    );
    
    Statistics.addLog(`找到 ${buttons.length} 个跟进按钮，当前索引: ${Statistics.currentIndex}`);
    





    
    // 如果没有找到按钮，返回 null
    if (!buttons.length) {
      Statistics.addLog('❌ 未找到跟进按钮，尝试点击重置按钮', 'error');
      // 查找并点击重置按钮
      const resetButton = document.querySelector("#app > section > section > main > section > div > form > div:nth-child(1) > div:nth-child(4) > div > div > button:nth-child(2)");
      if (resetButton) {
        await clickButton(resetButton);
        Statistics.addLog('🖱️ 点击重置按钮', 'info');
        // 等待一段时间后重试
        await wait(getOperationWaitTime());
      } else {
        Statistics.addLog('❌ 未找到重置按钮', 'error');
      }
      
      
      
      
      
      return null;
    }
    
    // 如果当前索引超出按钮数量，重置为0
    if (Statistics.currentIndex >= buttons.length) {
      Statistics.currentIndex = 0;
      Statistics.addLog('索引超出范围，重置为0');
    }
    
    // 返回当前索引对应的按钮
    const targetButton = buttons[Statistics.currentIndex];
    if (targetButton) {
      Statistics.addLog(`选择第 ${Statistics.currentIndex + 1} 个跟进按钮`);
      return targetButton;
    }
    
    return null;
  } catch (error) {
    Statistics.addLog(`❌ 查找跟进按钮失败: ${error.message}`, 'error');
    return null;
  }
}

// 处理升级通知弹窗
async function handleUpgradeDialog() {
  await wait(getOperationWaitTime() * 2);
  const dialog = document.querySelector('.el-dialog__wrapper');
  if (!dialog) return;

  const titleEl = dialog.querySelector('.el-dialog__title');
  if (!titleEl || titleEl.textContent !== '升级通知') return;

  const buttons = dialog.querySelectorAll('button');
  const closeButton = Array.from(buttons).find(btn => 
    btn.textContent.trim() === '关闭' || 
    btn.textContent.includes('关闭')
  );

  if (closeButton) {
    await clickButton(closeButton);
    await wait(getOperationWaitTime() * 2);
  }
}

// 查找文本输入框
async function findTextInput() {
  let attempts = 0;
  const maxAttempts = 6;

  while (attempts < maxAttempts) {
    const textarea = document.querySelector('textarea.el-textarea__inner');
    if (textarea) {
      const style = window.getComputedStyle(textarea);
      if (style.display !== 'none') {
        // 确保输入框在对话框内可见
        const dialogBody = document.querySelector('.el-dialog__body');
        if (dialogBody) {
          const textareaRect = textarea.getBoundingClientRect();
          const dialogRect = dialogBody.getBoundingClientRect();
          
          // 检查输入框是否在对话框可视区域内
          if (textareaRect.top < dialogRect.top || textareaRect.bottom > dialogRect.bottom) {
            // 滚动到输入框位置
            dialogBody.scrollTo({
              top: dialogBody.scrollTop + (textareaRect.top - dialogRect.top) - (dialogRect.height / 2) + (textareaRect.height / 2),
              behavior: 'smooth'
            });
            await wait(getOperationWaitTime());
          }
        }
        return textarea;
      }    // 4. 检测窗口是否自动关闭
      const maxWaitTime = 4000;
      const startTime = Date.now();
      const initialDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
      const initialCount = initialDialogs.length;
      let isAutoClose = false;  // 添加标记，记录是否是自动关闭
      
      while (Date.now() - startTime < maxWaitTime) {
        const currentDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
        if (currentDialogs.length < initialCount) {
          Statistics.addLog('✅ 检测到窗口已自动关闭');
          Statistics.addSuccess(); // 自动关闭，记为成功
          isAutoClose = true;  // 标记为自动关闭
          return true;
        }
        // 移除不必要的等待
      }

      // 5. 如果窗口未自动关闭，处理选择框
      if (!isAutoClose) {  // 只有在非自动关闭时才继续
        Statistics.addLog('⚠️ 窗口未自动关闭，直接再次点击保存');
  
        // 7. 再次检测窗口是否自动关闭
        const secondStartTime = Date.now();
        while (Date.now() - secondStartTime < maxWaitTime) {
          const currentDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
          if (currentDialogs.length < initialCount) {
            Statistics.addLog('✅ 检测到窗口已自动关闭');
            Statistics.addSuccess(); // 自动关闭，记为成功
            isAutoClose = true;  // 标记为自动关闭
            return true;
          }
          // 移除不必要的等待
        }
  
        // 8. 如果还是未自动关闭，尝试手动关闭（这种情况算作失败）
        if (!isAutoClose) {  // 确保只在非自动关闭时执行
          Statistics.addLog('⚠️ 窗口仍未自动关闭，尝试手动关闭', 'warning');
          Statistics.addFailure(); // 在这里只记录一次失败
          
          const closeButton = document.querySelector("#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__header > button > i") || 
                             document.querySelector("#app > section > section > main > section > div > div:nth-child(6) > div > div.el-dialog__header > button > i");
          
          if (closeButton) {
            Statistics.addLog('🖱️ 点击关闭按钮');
            closeButton.click();
            await wait(getOperationWaitTime());
            
            const finalDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
            if (finalDialogs.length < initialCount) {
              Statistics.addLog('✅ 手动关闭窗口成功，将跳过此条记录');
              Statistics.currentIndex++;
              Statistics.addLog(`📍 移动到第 ${Statistics.currentIndex + 1} 条记录`);
              await wait(getOperationWaitTime());
              return true;
            }
          }

          Statistics.addLog('❌ 窗口关闭失败，将跳过当前记录', 'warning');
          Statistics.currentIndex++;
          Statistics.addLog(`📍 移动到第 ${Statistics.currentIndex + 1} 条记录`);
          await wait(getOperationWaitTime());
        }
      }
      return true;
    }
    await wait(getOperationWaitTime());
    attempts++;
  }
  throw new Error('未找到文本输入框');
}

// 修改输入文本函数，添加智能表单填充功能
async function inputText(textInput) {
  try {
    // 1. 直接输入内容
    const dialogBody = document.querySelector('.el-dialog__body');
    if (dialogBody) {
    }

    // 2. 获取随机回复内容并输入文本
    const message = getRandomMessage();
    if (!message) {
      Statistics.addLog('❌ 无法获取有效消息', 'error');
      return false;
    }

    textInput.value = message;
    textInput.dispatchEvent(new InputEvent('input', { bubbles: true }));
    textInput.dispatchEvent(new Event('change', { bubbles: true }));
    await wait(getOperationWaitTime());
    Statistics.addLog('✍️ 文本输入完成');

    // 3. 智能表单自动填充（如果启用）
    if (settings && settings.autoFillForm) {
      await autoFillForm();
    } else {
      Statistics.addLog('⚠️ 智能表单填充已禁用，跳过');
    }

    // 4. 第一次点击保存按钮

    // 移除保存前等待，直接执行

    const saveButton = Array.from(document.querySelectorAll('button'))
      .find(btn => btn.textContent.includes('保存'));

    if (!saveButton) {
      Statistics.addLog('❌ 未找到保存按钮', 'error');
      return false;
    }

    Statistics.addLog('🖱️ 点击保存按钮');
    saveButton.click();
    await wait(1); // 最小必要等待

    // 4. 检测窗口是否自动关闭
    const maxWaitTime = 5000;
    const startTime = Date.now();
    const initialDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
    const initialCount = initialDialogs.length;

    // 使用智能检测窗口关闭
    const windowClosed = await smartDetectWindowClosed(initialCount, 100);

    if (windowClosed) {
      Statistics.addLog('✅ 检测到窗口已自动关闭');
      return true;
    }

    // 5. 如果窗口未自动关闭，直接再次点击保存
    Statistics.addLog('⚠️ 窗口未自动关闭，直接再次点击保存');

    // 6. 再次点击保存按钮
    Statistics.addLog('🖱️ 再次点击保存按钮');
    saveButton.click();
    await wait(getOperationWaitTime());

    // 7. 再次检测窗口是否自动关闭
    const secondStartTime = Date.now();
    while (Date.now() - secondStartTime < maxWaitTime) {
      const currentDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
      if (currentDialogs.length < initialCount) {
        Statistics.addLog('✅ 检测到窗口已自动关闭');
        return true;
      }
      await wait(1); // 最小检测间隔
    }

    // 8. 如果还是未自动关闭，尝试手动关闭
    Statistics.addLog('⚠️ 窗口仍未自动关闭，尝试手动关闭', 'warning');

    const closeButton = document.querySelector("#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__header > button > i") ||
                       document.querySelector("#app > section > section > main > section > div > div:nth-child(6) > div > div.el-dialog__header > button > i");

    if (closeButton) {
      Statistics.addLog('🖱️ 点击关闭按钮');
      closeButton.click();
      await wait(getOperationWaitTime());

      const finalDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
      if (finalDialogs.length < initialCount) {
        Statistics.addLog('✅ 手动关闭窗口成功，将跳过此条记录');
        Statistics.currentIndex++;
        Statistics.addLog(`📍 移动到第 ${Statistics.currentIndex + 1} 条记录`);
        await wait(getOperationWaitTime() * 2);
        return true;
      }
    }

    Statistics.addLog('❌ 窗口关闭失败，将跳过当前记录', 'warning');
    Statistics.currentIndex++;
    Statistics.addLog(`📍 移动到第 ${Statistics.currentIndex + 1} 条记录`);
    await wait(getOperationWaitTime() * 2);
    return true;

  } catch (error) {
    Statistics.addLog(`❌ 操作失败: ${error.message}`, 'error');
    throw error;
  }
}

async function performSingleFollow() {
  try {
    // 首先检查是否需要跳过当前记录
    if (Statistics.skipCurrentRecord) {
      Statistics.skipCurrentRecord = false;
      Statistics.moveToNext(); // 移动到下一条记录
      Statistics.addLog('⏭️ 跳过当前记录，继续处理下一条');
      await wait(1); // 移除不必要的跳过等待
      return true;
    }

    // 查找跟进按钮
    const followButton = await findFollowButton();
    if (!followButton) {
      Statistics.addFailure(true);
      Statistics.addLog('❌ 未找到跟进按钮，等待10秒后重试', 'error');
      // 如果当前索引不为0，重置索引
      if (Statistics.currentIndex > 0) {
        Statistics.currentIndex = 0;
        Statistics.addLog('🔄 重置到第一条记录');
      }
      await wait(10000);
      return false;
    }
    
    Statistics.addLog('找到跟进按钮，准备点击');
    await clickButton(followButton);
    Statistics.addLog('已点击跟进按钮');

    await handleUpgradeDialog();

    const textInput = await findTextInput();
    Statistics.addLog('找到文本输入框');

    // 继续正常的处理流程
    const result = await inputText(textInput);

    if (result) {
      // 只有在没有设置skipCurrentRecord时才计算成功
      if (!Statistics.skipCurrentRecord) {
        Statistics.addSuccess();
        Statistics.addLog('本次跟进操作完成', 'success');
      }

      const waitTime = getRandomWaitTime();
      const waitTimeDisplay = waitTime < 1 ?
        `${(waitTime * 1000).toFixed(0)}毫秒` :
        `${waitTime}秒`;
      Statistics.addLog(`⏳ 等待${waitTimeDisplay}后开始下一轮...`);
      await wait(waitTime);
      return true;
    } else {
      Statistics.addFailure();
      Statistics.addLog('❌ 跟进操作失败', 'error');
      await wait(10000);
      return false;
    }
  } catch (error) {
    Statistics.addFailure();
    Statistics.addLog(`❌ ${error.message}`, 'error');
    await wait(10000);
    return false;
  }
}

async function findTextInput() {
  let attempts = 0;
  const maxAttempts = 6;

  while (attempts < maxAttempts) {
    const textarea = document.querySelector('textarea.el-textarea__inner');
    if (textarea) {
      const style = window.getComputedStyle(textarea);
      if (style.display !== 'none') {
        // 确保输入框在对话框内可见
        const dialogBody = document.querySelector('.el-dialog__body');
        if (dialogBody) {
          const textareaRect = textarea.getBoundingClientRect();
          const dialogRect = dialogBody.getBoundingClientRect();
          
          // 检查输入框是否在对话框可视区域内
          if (textareaRect.top < dialogRect.top || textareaRect.bottom > dialogRect.bottom) {
            // 滚动到输入框位置
            dialogBody.scrollTo({
              top: dialogBody.scrollTop + (textareaRect.top - dialogRect.top) - (dialogRect.height / 2) + (textareaRect.height / 2),
              behavior: 'smooth'
            });
            // 移除滚动等待，现代浏览器滚动很快
          }
        }
        return textarea;
      }    // 4. 检测窗口是否自动关闭
      const maxWaitTime = 4000;
      const startTime = Date.now();
      const initialDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
      const initialCount = initialDialogs.length;
      let isAutoClose = false;  // 添加标记，记录是否是自动关闭
      
      while (Date.now() - startTime < maxWaitTime) {
        const currentDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
        if (currentDialogs.length < initialCount) {
          Statistics.addLog('✅ 检测到窗口已自动关闭');
          Statistics.addSuccess(); // 自动关闭，记为成功
          isAutoClose = true;  // 标记为自动关闭
          return true;
        }
        await wait(100);
      }

      // 5. 如果窗口未自动关闭，处理选择框
      if (!isAutoClose) {  // 只有在非自动关闭时才继续
        Statistics.addLog('⚠️ 窗口未自动关闭，直接再次点击保存');
  
        // 7. 再次检测窗口是否自动关闭
        const secondStartTime = Date.now();
        while (Date.now() - secondStartTime < maxWaitTime) {
          const currentDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
          if (currentDialogs.length < initialCount) {
            Statistics.addLog('✅ 检测到窗口已自动关闭');
            Statistics.addSuccess(); // 自动关闭，记为成功
            isAutoClose = true;  // 标记为自动关闭
            return true;
          }
          await wait(100);
        }
  
        // 8. 如果还是未自动关闭，尝试手动关闭（这种情况算作失败）
        if (!isAutoClose) {  // 确保只在非自动关闭时执行
          Statistics.addLog('⚠️ 窗口仍未自动关闭，尝试手动关闭', 'warning');
          Statistics.addFailure(); // 在这里只记录一次失败
          
          const closeButton = document.querySelector("#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__header > button > i") || 
                             document.querySelector("#app > section > section > main > section > div > div:nth-child(6) > div > div.el-dialog__header > button > i");
          
          if (closeButton) {
            Statistics.addLog('🖱️ 点击关闭按钮');
            closeButton.click();
            await wait(200);
            
            const finalDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
            if (finalDialogs.length < initialCount) {
              Statistics.addLog('✅ 手动关闭窗口成功，将跳过此条记录');
              Statistics.currentIndex++;
              Statistics.addLog(`📍 移动到第 ${Statistics.currentIndex + 1} 条记录`);
              await wait(500);
              return true;
            }
          }

          Statistics.addLog('❌ 窗口关闭失败，将跳过当前记录', 'warning');
          Statistics.currentIndex++;
          Statistics.addLog(`📍 移动到第 ${Statistics.currentIndex + 1} 条记录`);
          await wait(500);
        }
      }
      return true;
    }
    await wait(300);
    attempts++;
  }
  throw new Error('未找到文本输入框');
}

// 添加检查元素是否在容器可视区域内的辅助函数
function isElementVisible(element, container) {
  const elementRect = element.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();
  
  return (
    elementRect.top >= containerRect.top &&
    elementRect.bottom <= containerRect.bottom
  );
}

// 修改 performSingleFollow 函数
async function performSingleFollow() {
  try {
    // 首先检查是否需要跳过当前记录
    if (Statistics.skipCurrentRecord) {
      Statistics.skipCurrentRecord = false;
      Statistics.moveToNext(); // 移动到下一条记录
      Statistics.addLog('⏭️ 跳过当前记录，继续处理下一条');
      await wait(1000);
      return true;
    }

    // 查找跟进按钮
    const followButton = await findFollowButton();
    if (!followButton) {
      Statistics.addFailure(true);
      Statistics.addLog('❌ 未找到跟进按钮，等待10秒后重试', 'error');
      // 如果当前索引不为0，重置索引
      if (Statistics.currentIndex > 0) {
        Statistics.currentIndex = 0;
        Statistics.addLog('🔄 重置到第一条记录');
      }
      await wait(10000);
      return false;
    }
    
    Statistics.addLog('找到跟进按钮，准备点击');
    await clickButton(followButton);
    Statistics.addLog('已点击跟进按钮');
    
    await handleUpgradeDialog();
    
    const textInput = await findTextInput();
    Statistics.addLog('找到文本输入框');

    // 继续正常的处理流程
    const result = await inputText(textInput);
    
    if (result) {
      // 只有在没有设置skipCurrentRecord时才计算成功
      if (!Statistics.skipCurrentRecord) {
        Statistics.addSuccess();
        Statistics.addLog('本次跟进操作完成', 'success');
      }
      
      const waitTime = getRandomWaitTime();
      const waitTimeDisplay = waitTime < 1 ? 
        `${(waitTime * 1000).toFixed(0)}毫秒` : 
        `${waitTime}秒`;
      Statistics.addLog(`⏳ 等待${waitTimeDisplay}后开始下一轮...`);
      await wait(waitTime);
      return true;
    } else {
      Statistics.addFailure();
      Statistics.addLog('❌ 跟进操作失败', 'error');
      await wait(10000);
      return false;
    }
  } catch (error) {
    Statistics.addFailure();
    Statistics.addLog(`❌ ${error.message}`, 'error');
    await wait(10000);
    return false;
  }
}

// 添加查找下一条按钮的函数
async function findNextButton() {
  try {
    // 等待下一条按钮出现
    const nextButton = await waitForElement('button.el-button--default:not(.el-button--primary):not(.is-disabled)');
    if (nextButton && nextButton.textContent.includes('下一条')) {
      return nextButton;
    }
    return null;
  } catch (error) {
    Statistics.addLog('⚠️ 未找到下一条按钮', 'warning');
    return null;
  }
}

// 添加等待元素出现的辅助函数
async function waitForElement(selector, timeout = 5000) {
  const startTime = Date.now();
  while (Date.now() - startTime < timeout) {
    const element = document.querySelector(selector);
    if (element) {
      return element;
    }
    await wait(100);
  }
  return null;
}

// 优化开始自动化流程函数
async function startAutomation() {
  Statistics.start();
  Statistics.addLog('开始自动化流程');
  
  let consecutiveFailures = 0;
  const maxConsecutiveFailures = 3;
  const sleepTime = 30; // 30分钟
  
  while (isRunning) {
    try {
      Statistics.totalAttempts++;
      const success = await performSingleFollow();
      
      if (success) {
        consecutiveFailures = 0;
      } else {
        consecutiveFailures++;
        if (consecutiveFailures >= maxConsecutiveFailures) {
          // 进入休眠状态
          Statistics.setSleepMode(sleepTime);
          
          // 使用 while 循环代替 for 循环，以便更好地处理停止
          let sleepStartTime = Date.now();
          while (isRunning && Statistics.isSleeping && Date.now() - sleepStartTime < sleepTime * 60000) {
            await wait(1000); // 每秒检查一次
          }
          
          // 如果是因为停止而退出循环，确保清理状态
          if (!isRunning) {
            Statistics.clearSleepMode();
            break;
          }
          
          if (isRunning) {
            Statistics.clearSleepMode();
            Statistics.addLog('🔄 重新开始运行');
            consecutiveFailures = 0;
          }
        }
      }
    } catch (error) {
      consecutiveFailures++;
      if (consecutiveFailures >= maxConsecutiveFailures) {
        Statistics.setSleepMode(sleepTime);
        
        // 同样使用 while 循环处理休眠
        let sleepStartTime = Date.now();
        while (isRunning && Statistics.isSleeping && Date.now() - sleepStartTime < sleepTime * 60000) {
          await wait(1000);
        }
        
        // 如果是因为停止而退出循环，确保清理状态
        if (!isRunning) {
          Statistics.clearSleepMode();
          break;
        }
        
        if (isRunning) {
          Statistics.clearSleepMode();
          Statistics.addLog('🔄 重新开始运行');
          consecutiveFailures = 0;
        }
      } else {
        await wait(5000);
      }
    }
  }
  
  // 确保在退出时清理所有状态
  if (Statistics.isSleeping) {
    Statistics.clearSleepMode();
  }
  Statistics.addLog('自动化流程已停止');
}

// 修改 getRandomMessage 函数，添加默认消息和错误处理
function getRandomMessage() {
  // 默认消息列表
  const defaultMessages = [
    "您好，请问您最近有看车计划吗？",
    "您好，最近有考虑购车吗？",
    "请问您对哪款车型比较感兴趣呢？",
    "您好，需要了解具体车型的信息吗？",
    "最近店内有优惠活动，您有兴趣了解一下吗？"
  ];

  try {
    // 检查 settings 是否存在
    if (!settings || !settings.messages || !Array.isArray(settings.messages) || settings.messages.length === 0) {

      return defaultMessages[Math.floor(Math.random() * defaultMessages.length)];
    }

    // 过滤有效消息
    const validMessages = settings.messages.filter(msg => msg && typeof msg === 'string' && msg.trim());
    
    if (validMessages.length === 0) {
      Statistics.addLog('⚠️ 没有有效的自定义消息，使用默认消息');
      return defaultMessages[Math.floor(Math.random() * defaultMessages.length)];
    }

    return validMessages[Math.floor(Math.random() * validMessages.length)];
  } catch (error) {
    Statistics.addLog('⚠️ 获取消息出错，使用默认消息');
    return defaultMessages[Math.floor(Math.random() * defaultMessages.length)];
  }
}

// 修改获取随机等待时间的函数（用于跟进操作之间的等待）
function getRandomWaitTime() {
  // 将输入值转换为浮点数，支持小数
  const minTime = parseFloat(settings?.minWaitTime) || 0.1;
  const maxTime = parseFloat(settings?.maxWaitTime) || 0.2;

  // 确保最小值不小于0.1秒
  const safeMinTime = Math.max(0.1, minTime);
  const safeMaxTime = Math.max(safeMinTime, maxTime);

  // 生成随机等待时间（支持小数）
  const randomTime = Math.round((Math.random() * (safeMaxTime - safeMinTime) + safeMinTime) * 10) / 10;

  return randomTime;
}

// 新增：获取操作等待时间的函数（用于页面操作之间的等待）
function getOperationWaitTime() {
  // 将输入值转换为浮点数，支持小数
  const minTime = parseFloat(settings?.minWaitTime) || 0.1;
  const maxTime = parseFloat(settings?.maxWaitTime) || 0.2;

  // 确保最小值不小于0.05秒，最大值不超过0.5秒（页面操作应该更快）
  const safeMinTime = Math.max(0.05, Math.min(minTime, 0.3));
  const safeMaxTime = Math.max(safeMinTime, Math.min(maxTime, 0.5));

  // 生成随机等待时间（支持小数）
  const randomTime = Math.random() * (safeMaxTime - safeMinTime) + safeMinTime;
  return Math.round(randomTime * 100) / 100; // 保留2位小数
}

// 修改等待函数，支持小数秒
function wait(ms) {
  // 如果输入是秒，转换为毫秒
  if (ms < 100) { // 假设小于100的是秒单位
    ms = ms * 1000;
  }
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 极速智能检测：等待条件满足
async function fastWaitForCondition(conditionFn, maxAttempts = 50) {
  for (let i = 0; i < maxAttempts; i++) {
    const result = conditionFn();
    if (result) {
      return result; // 返回条件函数的实际结果
    }
    await wait(1); // 1ms极速检测
  }
  return false;
}

// 极速智能检测：等待元素可见
async function fastWaitForVisible(selector, maxAttempts = 50) {
  return fastWaitForCondition(() => {
    const element = document.querySelector(selector);
    return element && element.offsetParent !== null;
  }, maxAttempts);
}

// 智能检测窗口是否关闭
async function smartDetectWindowClosed(initialCount, maxAttempts = 100) {
  return fastWaitForCondition(() => {
    // 方法1: 检查对话框数量
    const currentDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
    const visibleDialogs = Array.from(currentDialogs).filter(dialog => {
      const style = window.getComputedStyle(dialog);
      return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
    });

    // 方法2: 检查对话框内容是否存在
    const dialogBodies = document.querySelectorAll('.el-dialog__body');
    const visibleBodies = Array.from(dialogBodies).filter(body => {
      const style = window.getComputedStyle(body);
      return style.display !== 'none' && style.visibility !== 'hidden';
    });

    // 方法3: 检查遮罩层
    const masks = document.querySelectorAll('.el-dialog__wrapper .v-modal');
    const visibleMasks = Array.from(masks).filter(mask => {
      const style = window.getComputedStyle(mask);
      return style.display !== 'none' && style.visibility !== 'hidden';
    });

    return visibleDialogs.length < initialCount ||
           visibleBodies.length === 0 ||
           visibleMasks.length === 0;
  }, maxAttempts);
}

// 消息监听
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.action === 'start') {
    if (!isRunning) {
      isRunning = true;
      settings = request.settings;

      // 简化设置日志
      if (settings.minWaitTime && settings.maxWaitTime) {
        Statistics.addLog(`⚙️ 等待时间设置: ${settings.minWaitTime}-${settings.maxWaitTime}秒`);
      }
      
      // 立即更新状态
      Statistics.updatePopup();
      
      // 发送立即状态更新
      chrome.runtime.sendMessage({
        action: 'updateSleepStatus',
        data: {
          isSleeping: Statistics.isSleeping,
          remainingTime: 0,
          sleepEndTime: null,
          isRunning: true
        }
      });

      startAutomation().catch(error => {
        console.error('自动化流程错误:', error);
        Statistics.addLog('运行错误: ' + error.message, 'error');
        isRunning = false;
        // 出错时更新状态
        Statistics.updatePopup();
      });
      
      sendResponse({ 
        status: 'started',
        currentState: {
          isRunning: true,
          isSleeping: Statistics.isSleeping,
          stats: Statistics.getStats()
        }
      });
    } else {
      sendResponse({ status: 'error', error: '已经在运行中' });
    }
    return true;
  }

  if (request.action === 'stop') {
    isRunning = false;
    
    // 立即清除休眠状态
    if (Statistics.isSleeping) {
      Statistics.clearSleepMode();
    }
    
    // 立即发送停止状态
    chrome.runtime.sendMessage({
      action: 'updateSleepStatus',
      data: {
        isSleeping: false,
        timeDisplay: '',
        remainingMs: 0,
        isRunning: false
      }
    });

    Statistics.addLog('停止自动化流程');
    Statistics.updatePopup();
    
    sendResponse({ status: 'stopped' });
    return true;
  }

  if (request.action === 'getStats') {
    sendResponse(Statistics.getStats());
  }

  if (request.action === 'getState') {
    sendResponse({
      isRunning,
      stats: Statistics.getStats()
    });
    return true;
  }

  if (request.action === 'resetStats') {
    Statistics.reset();
    sendResponse({ success: true });
    return true;
  }
});

// 发送就绪消息
console.log('[自动跟进助手] Content script准备就绪');
chrome.runtime.sendMessage({
  action: 'contentScriptReady',
  url: window.location.href
});

// 辅助函数：获取元素的路径
function getElementPath(element) {
  const path = [];
  while (element && element.nodeType === Node.ELEMENT_NODE) {
    let selector = element.nodeName.toLowerCase();
    if (element.id) {
      selector += `#${element.id}`;
    } else if (element.className) {
      selector += `.${element.className.replace(/\s+/g, '.')}`;
    }
    path.unshift(selector);
    element = element.parentNode;
  }
  return path.join(' > ');
}

// 添加事件监听器的辅助函数
function addPassiveEventListener(element, eventType, handler) {
  element.addEventListener(eventType, handler, { passive: true });
}

// 修改点击按钮函数，移除验证逻辑
async function clickButton(button, retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      const scrollOptions = {
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      };

      await new Promise(resolve => {
        requestAnimationFrame(() => {
          button.scrollIntoView(scrollOptions);
          setTimeout(resolve, 500);
        });
      });

      const eventOptions = {
        bubbles: true,
        cancelable: true,
        view: window,
        passive: true
      };

      // 确保按钮可见且可点击
      const style = window.getComputedStyle(button);
      if (style.display === 'none' || style.visibility === 'hidden') {
        throw new Error('按钮不可见');
      }

      const events = ['mouseenter', 'mousedown', 'mouseup', 'click'];
      for (const eventType of events) {
        const event = new MouseEvent(eventType, eventOptions);
        button.dispatchEvent(event);
        await wait(100);
      }
      return;

    } catch (error) {
      if (i === retries - 1) {
        throw error;
      }
      await wait(1); // 最小重试间隔
    }
  }
}

// 辅助函数：等待元素出现
async function waitForElement(selector, timeout = 3000) {
  const startTime = Date.now();
  while (Date.now() - startTime < timeout) {
    const element = document.querySelector(selector);
    if (element && element.offsetParent !== null) { // 确保元素可见
      return element;
    }
    await wait(100);
  }
  return null;
}

// 处理选择框的通用函数
async function handleSelect(type, selector) {
  try {
    Statistics.addLog(`检查${type}选择框`);
    
    // 使用提供的选择器查找选择框
    const select = document.querySelector(selector);
    // 修改备用选择器，确保能找到正确的元素
    const altSelect = document.querySelector(`.el-dialog__body .el-select input[placeholder="请选择"]`);
    
    // 合并选择器检查
    const targetSelect = select || altSelect;
    if (!targetSelect) {
      Statistics.addLog(`未找到${type}选择框，可能不需要选择`, 'info');
      return true;
    }

    // 检查是否为"请选择"状态
    const input = targetSelect.querySelector('input[placeholder="请选择"]');
    if (!input || input.value !== '') {
      Statistics.addLog(`${type}已选择，无需操作`, 'info');
      return true;
    }

    // 点击选择框并等待下拉框
    targetSelect.click();
    // 智能检测下拉框是否展开
    await fastWaitForVisible('.el-select-dropdown:not([style*="display: none"])');
    
    // 查找可见的下拉框
    const visibleDropdown = document.querySelector('.el-select-dropdown:not([style*="display: none"])');
    if (!visibleDropdown) {
      Statistics.addLog('未找到可见的下拉框', 'warning');
      return false;
    }

    // 在可见下拉框中查找第一个可用选项
    const firstOption = visibleDropdown.querySelector('.el-select-dropdown__item:not(.is-disabled)');
    if (!firstOption) {
      Statistics.addLog(`未找到${type}可用选项`, 'warning');
      return false;
    }

    // 点击第一个选项
    firstOption.click();
    await wait(1); // 最小必要等待
    
    // 验证选择是否成功
    if (targetSelect.value === '请选择') {
      Statistics.addLog(`${type}选择可能未成功，重试一次`, 'warning');
      firstOption.click();
      await wait(500);
    }

    Statistics.addLog(`已选择${type}选项`);
    return true;

  } catch (error) {
    Statistics.addLog(`${type}选择失败: ${error.message}`, 'error');
    return false;
  }
}

// 必填选择框配置（按网页顺序）
const REQUIRED_FIELD_CONFIG = {
  // 按照网页上的顺序排列
  '线索是否有效': {
    options: ['有效线索', '待定'],
    defaultValue: '有效线索',
    required: true
  },
  '意向车系': {
    options: ['Q5L', 'A4L Limousine', 'A6L Limousine', 'Q3', 'A3 Limousine'],
    defaultValue: 'Q5L',
    required: true
  },
  '预购日期': {
    type: 'date',
    required: true
  },
  '线索等级': {
    options: ['B（30天内跟进）', 'A（7天内跟进）', 'H（2天内跟进）'],
    defaultValue: 'B（30天内跟进）',
    required: true,
    randomSelect: true  // 启用随机选择
  },
  '跟进状态': {
    options: ['再次待跟进', '有意向到店', '已到店交接', '申请无效'],
    defaultValue: '再次待跟进',
    required: true
  },
  '跟进方式': {
    options: ['电话沟通', '微信交流', '面对面沟通'],
    defaultValue: '电话沟通',
    required: true
  },
  '计划跟进时间': {
    type: 'date',
    required: true
  }
};

// 智能表单自动填充函数
async function autoFillForm() {
  try {
    Statistics.addLog('🤖 开始智能表单填充');
    let filledCount = 0;

    // 等待表单加载
    await wait(getOperationWaitTime());

    // 只处理必填字段（带*号），完全跳过非必填选择框
    Statistics.addLog('📋 跳过所有非必填选择框，只处理带*号的必填字段');

    Statistics.addLog('🔍 智能检测模式：只处理空的必填字段');
    await forceHandleRequiredFields();

    filledCount = 1; // 文本输入算作1个字段

    // 注意：必填的日期字段（预购日期、计划跟进时间）已在上面处理
    // 这里只处理其他非必填的日期字段（如果需要的话）
    Statistics.addLog('📅 必填日期字段已处理，跳过其他日期字段');

    // 处理文本区域
    await fillTextAreas();

    Statistics.addLog(`✅ 表单填充完成，共填充 ${filledCount} 个字段`, 'success');
    return true;

  } catch (error) {
    Statistics.addLog(`❌ 表单填充失败: ${error.message}`, 'error');
    return false;
  }
}



// 强制处理必填字段（带*号）- 使用精确路径
async function forceHandleRequiredFields() {
  try {
    Statistics.addLog('🔍 开始智能检测必填字段（只处理空字段）');

    // 使用精确的CSS选择器路径处理关键必填字段
    await handleRequiredFieldByPath();

  } catch (error) {
    Statistics.addLog(`处理必填字段失败: ${error.message}`, 'error');
  }
}

// 智能检测空的必填字段
async function detectEmptyRequiredFields(requiredFields) {
  const fieldsToProcess = [];

  for (const field of requiredFields) {
    try {
      const element = document.querySelector(field.path);
      if (!element) {
        Statistics.addLog(`⚠️ 未找到${field.name}元素，将尝试处理`);
        fieldsToProcess.push(field);
        continue;
      }

      let isEmpty = false;
      let formControl = null;

      // 如果找到的是label，需要找到对应的表单控件
      if (element.tagName === 'LABEL') {
        const formItem = element.closest('.el-form-item');
        if (formItem) {
          if (field.type === 'select') {
            formControl = formItem.querySelector('.el-select');
          } else if (field.type === 'date') {
            formControl = formItem.querySelector('.el-date-editor');
          }
        }
      } else {
        formControl = element;
      }

      if (!formControl) {
        Statistics.addLog(`⚠️ 未找到${field.name}对应的表单控件，将尝试处理`);
        fieldsToProcess.push(field);
        continue;
      }

      if (field.type === 'select') {
        // 检查选择框是否有值
        const selectInput = formControl.querySelector('input, .el-input__inner');
        if (selectInput) {
          const value = selectInput.value || selectInput.placeholder;
          isEmpty = !value || value.trim() === '' || value.includes('请选择') || value.includes('全部');

          Statistics.addLog(`🔍 ${field.name}当前值: "${value}"`);

          // 特殊处理线索等级：支持随机选择或确保为30天
          if (field.name === '线索等级') {
            // 如果启用随机选择，总是处理该字段
            if (settings && settings.randomLevelSelect) {
              fieldsToProcess.push(field);
              Statistics.addLog(`🎲 线索等级启用随机选择模式，当前值: "${value}"`);
            } else {
              // 未启用随机选择：检查是否为30天，如果不是则修改
              if (isEmpty) {
                fieldsToProcess.push(field);
                Statistics.addLog(`🔍 线索等级为空，将设置为30天`);
              } else if (!value.includes('30天')) {
                fieldsToProcess.push(field);
                Statistics.addLog(`🔧 线索等级当前为"${value}"，不是30天，需要修改为30天`);
              } else {
                Statistics.addLog(`🔍 线索等级已有值"${value}"，跳过处理`);
              }
            }
          } else {
            // 智能检测模式：只处理空字段
            if (isEmpty) {
              fieldsToProcess.push(field);
            }
          }
        } else {
          isEmpty = true;
          Statistics.addLog(`🔍 ${field.name}未找到输入框，视为空`);
          fieldsToProcess.push(field);
        }
      } else if (field.type === 'date') {
        // 检查日期字段是否有值
        const dateInput = formControl.querySelector('input');
        if (dateInput) {
          const value = dateInput.value;
          isEmpty = !value || value.trim() === '';

          Statistics.addLog(`🔍 ${field.name}当前值: "${value}"`);

          // 特殊处理计划跟进时间的随机选择
          if (field.name === '计划跟进时间') {
            if (settings && settings.randomFollowTime) {
              fieldsToProcess.push(field);
              Statistics.addLog(`🎲 计划跟进时间启用随机选择模式，当前值: "${value}"`);
            } else {
              // 未启用随机选择：只处理空值
              if (isEmpty) {
                fieldsToProcess.push(field);
                Statistics.addLog(`🔍 计划跟进时间为空，将设置默认值`);
              } else {
                Statistics.addLog(`🔍 计划跟进时间已有值"${value}"，跳过处理`);
              }
            }
          } else {
            // 其他日期字段：智能检测模式，只处理空字段
            if (isEmpty) {
              fieldsToProcess.push(field);
            }
          }
        } else {
          isEmpty = true;
          Statistics.addLog(`🔍 ${field.name}未找到输入框，视为空`);
          fieldsToProcess.push(field);
        }
      }

    } catch (error) {
      Statistics.addLog(`⚠️ 检测${field.name}时出错: ${error.message}，将尝试处理`);
      fieldsToProcess.push(field);
    }
  }

  return fieldsToProcess;
}

// 使用精确路径处理必填字段 - 智能检测版本
async function handleRequiredFieldByPath() {
  try {
    // 定义所有必填字段的配置（按网页顺序）
    const requiredFields = [
      {
        name: '线索是否有效',
        path: "label[for='isInvalid']",
        type: 'select',
        options: ['有效线索', '待定']
      },
      {
        name: '意向车系',
        path: "label[for='intentionSeries']",
        type: 'select',
        options: ['Q5L', 'A4L Limousine', 'A6L Limousine', 'Q3', 'A3 Limousine']
      },
      {
        name: '预购日期',
        path: "label[for='buyCarDate']",
        type: 'date'
      },
      {
        name: '线索等级',
        path: "label[for='level']",
        type: 'select',
        options: ['B（30天内跟进）', 'A（7天内跟进）', 'H（2天内跟进）']
      },
      {
        name: '跟进状态',
        path: "label[for='nextState']",
        type: 'select',
        options: ['再次待跟进', '有意向到店', '已到店交接', '申请无效']
      },
      {
        name: '跟进方式',
        path: "label[for='followMethod']",
        type: 'select',
        options: ['电话沟通', '微信交流', '面对面沟通']
      },
      {
        name: '计划跟进时间',
        path: "label[for='nextFollowTime']",
        type: 'date'
      }
    ];

    // 智能检测需要处理的字段
    const fieldsToProcess = await detectEmptyRequiredFields(requiredFields);

    if (fieldsToProcess.length === 0) {
      Statistics.addLog('✅ 所有必填字段都已有值，无需处理');
      return;
    }

    Statistics.addLog(`🔍 检测到 ${fieldsToProcess.length} 个必填字段需要处理: ${fieldsToProcess.map(f => f.name).join(', ')}`);

    // 逐个处理需要填充的字段
    for (const field of fieldsToProcess) {
      try {
    

        let success = false;
        if (field.type === 'select') {
          success = await handleSelectByPath(field.path, field.name, field.options);
        } else if (field.type === 'date') {
          success = await handleElementUIDatePicker(field.path, field.name);
        }

        if (success) {
          Statistics.addLog(`✅ ${field.name}处理成功`);
        }

        await wait(getOperationWaitTime());

      } catch (error) {
        Statistics.addLog(`❌ 处理${field.name}时出错: ${error.message}`, 'error');
      }
    }



  } catch (error) {
    Statistics.addLog(`精确路径处理失败: ${error.message}`, 'error');
  }
}

// 使用精确路径处理选择框
async function handleSelectByPath(cssPath, fieldName, expectedOptions) {
  try {
    const element = document.querySelector(cssPath);
    if (!element) {
      Statistics.addLog(`❌ 未找到${fieldName}元素: ${cssPath}`);
      return false;
    }

    // 如果找到的是label，需要找到对应的表单控件
    let selectBox = null;
    if (element.tagName === 'LABEL') {
      // 通过label找到对应的表单项
      const formItem = element.closest('.el-form-item');
      if (formItem) {
        selectBox = formItem.querySelector('.el-select');
      }
    } else {
      selectBox = element.querySelector('.el-select') || element.closest('.el-select') || element;
    }

    if (!selectBox) {
      Statistics.addLog(`❌ ${fieldName}中未找到选择框`);
      return false;
    }

    // 滚动到选择框位置
    selectBox.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await wait(500);

    // 强制点击选择框

    selectBox.click();
    // 智能检测选项是否加载完成
    await fastWaitForCondition(() => {
      const dropdown = document.querySelector('.el-select-dropdown:not([style*="display: none"])');
      return dropdown && dropdown.querySelectorAll('.el-select-dropdown__item').length > 0;
    });

    // 查找下拉选项
    let dropdown = null;
    for (let i = 0; i < 5; i++) {
      const dropdowns = document.querySelectorAll('.el-select-dropdown:not([style*="display: none"])');
      if (dropdowns.length > 0) {
        dropdown = dropdowns[dropdowns.length - 1];
        break;
      }
      await wait(300);
    }

    if (!dropdown) {
      Statistics.addLog(`❌ ${fieldName}下拉框未出现`);
      return false;
    }

    const options = dropdown.querySelectorAll('.el-select-dropdown__item');


    if (options.length > 0) {
      // 过滤有效选项
      const validOptions = Array.from(options).filter(opt => {
        const text = opt.textContent.trim();
        return text !== '' && text !== '全部' && text !== '请选择';
      });

      let selectedOption = null;

      // 特殊处理线索等级的随机选择
      const enableRandomSelect = (fieldName === '线索等级' && settings && settings.randomLevelSelect);

      if (enableRandomSelect && validOptions.length > 0) {
        // 随机选择模式
        const randomIndex = Math.floor(Math.random() * validOptions.length);
        selectedOption = validOptions[randomIndex];
        Statistics.addLog(`🎲 随机选择线索等级 (${randomIndex + 1}/${validOptions.length}): ${selectedOption.textContent.trim()}`);
      } else if (fieldName === '线索等级') {
        // 线索等级非随机模式：确保选择30天的选项
        Statistics.addLog(`🎯 线索等级非随机模式：查找30天选项`);

        // 优先查找包含"B"和"30天"的选项
        selectedOption = validOptions.find(opt => {
          const text = opt.textContent.trim();
          return text.includes('B') && text.includes('30天');
        });

        // 如果没找到B（30天），查找任何包含"30天"的选项
        if (!selectedOption) {
          selectedOption = validOptions.find(opt => {
            const text = opt.textContent.trim();
            return text.includes('30天');
          });
        }

        // 如果还没找到30天的选项，按原有逻辑查找
        if (!selectedOption) {
          for (const expectedOption of expectedOptions) {
            selectedOption = validOptions.find(opt =>
              opt.textContent.trim() === expectedOption
            );
            if (selectedOption) break;
          }
        }

        // 最后选择第一个有效选项
        if (!selectedOption && validOptions.length > 0) {
          selectedOption = validOptions[0];
          Statistics.addLog(`⚠️ 未找到30天选项，选择第一个可用选项: ${selectedOption.textContent.trim()}`);
        }

        if (selectedOption) {
          Statistics.addLog(`✅ 线索等级选择30天选项: ${selectedOption.textContent.trim()}`);
        }
      } else {
        // 其他字段：原有逻辑：按预期选项顺序查找
        for (const expectedOption of expectedOptions) {
          selectedOption = validOptions.find(opt =>
            opt.textContent.trim() === expectedOption
          );
          if (selectedOption) break;
        }

        // 如果没找到预期选项，选择第一个有效选项
        if (!selectedOption && validOptions.length > 0) {
          selectedOption = validOptions[0];
        }
      }

      if (selectedOption) {
        selectedOption.click();
        await wait(getOperationWaitTime());

        // 关闭下拉框
        document.body.click();
        await wait(getOperationWaitTime());

        Statistics.addLog(`✅ ${fieldName}设置成功: ${selectedOption.textContent.trim()}`);
        return true;
      }
    }

    Statistics.addLog(`❌ ${fieldName}未找到合适选项`);
    document.body.click(); // 关闭下拉框
    return false;

  } catch (error) {
    Statistics.addLog(`${fieldName}处理失败: ${error.message}`, 'error');
    return false;
  }
}

// 使用精确路径处理日期字段 - 模拟真实用户交互
async function handleDateByPath(cssPath, fieldName) {
  try {
    Statistics.addLog(`🔍 查找${fieldName}日期编辑器: ${cssPath}`);

    // 直接定位到el-date-editor元素
    const dateEditor = document.querySelector(cssPath);
    if (!dateEditor) {
      Statistics.addLog(`❌ 未找到${fieldName}日期编辑器`);
      return false;
    }

    Statistics.addLog(`✅ 找到${fieldName}日期编辑器`);

    // 滚动到元素位置
    dateEditor.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await wait(500);

    // 查找日期编辑器内的输入框
    const dateInput = dateEditor.querySelector('input') || dateEditor.querySelector('.el-input__inner');
    if (!dateInput) {
      Statistics.addLog(`❌ ${fieldName}日期编辑器中未找到输入框`);
      return false;
    }

    Statistics.addLog(`✅ 找到${fieldName}输入框`);

    // 生成未来日期
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);
    futureDate.setHours(10 + Math.floor(Math.random() * 8));
    futureDate.setMinutes(Math.floor(Math.random() * 60));

    const year = futureDate.getFullYear();
    const month = String(futureDate.getMonth() + 1).padStart(2, '0');
    const day = String(futureDate.getDate()).padStart(2, '0');
    const hour = String(futureDate.getHours()).padStart(2, '0');
    const minute = String(futureDate.getMinutes()).padStart(2, '0');
    const dateString = `${year}-${month}-${day} ${hour}:${minute}`;

    Statistics.addLog(`📅 准备通过模拟交互设置${fieldName}: ${dateString}`);

    // 模拟真实用户交互：点击输入框打开日期选择器
    Statistics.addLog(`📅 点击${fieldName}输入框打开日期选择器`);

    // 多次尝试点击，确保日期选择器打开
    for (let clickAttempt = 1; clickAttempt <= 3; clickAttempt++) {
      dateInput.click();
      // 智能检测日期选择器是否打开
      const opened = await fastWaitForVisible('.el-picker-panel:not([style*="display: none"])');
      if (opened) break;

      // 检查日期选择器是否打开
      const panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
      Statistics.addLog(`📅 第${clickAttempt}次点击后找到 ${panels.length} 个日期选择器面板`);

      if (panels.length > 0) {
        Statistics.addLog(`✅ 日期选择器已打开`);
        break;
      }

      if (clickAttempt < 3) {
        Statistics.addLog(`⚠️ 日期选择器未打开，重试点击`);
      }
    }

    // 查找并点击"此刻"按钮
    const nowButtonSelector = "body > div.el-picker-panel.el-date-picker.el-popper.has-time > div.el-picker-panel__footer > button.el-button.el-picker-panel__link-btn.el-button--text.el-button--mini";

    for (let attempt = 1; attempt <= 5; attempt++) {
      try {
        Statistics.addLog(`📅 第${attempt}次尝试查找"此刻"按钮`);

        // 智能检测"此刻"按钮是否可用
        await fastWaitForVisible('.el-picker-panel__footer button');

        // 调试：查找所有可能的日期选择器面板
        const allPanels = document.querySelectorAll('.el-picker-panel');
        Statistics.addLog(`🔍 找到 ${allPanels.length} 个日期选择器面板`);

        // 调试：查找所有可能的按钮
        const allButtons = document.querySelectorAll('.el-picker-panel__footer button');
        Statistics.addLog(`🔍 找到 ${allButtons.length} 个日期选择器按钮`);

        if (allButtons.length > 0) {
          const buttonTexts = Array.from(allButtons).map(btn => btn.textContent.trim());
          Statistics.addLog(`🔍 按钮文本: ${buttonTexts.join(', ')}`);
        }

        const nowButton = document.querySelector(nowButtonSelector);
        if (nowButton && nowButton.offsetParent !== null) { // 确保按钮可见
          Statistics.addLog(`✅ 找到"此刻"按钮，准备点击`);
          nowButton.click();
          // 智能检测日期是否已设置
          await fastWaitForCondition(() => {
            const input = document.querySelector(`${cssPath} input`);
            return input && input.value && input.value.trim() !== '';
          });

          // 查找输入框验证是否设置成功
          const dateInput = document.querySelector(`${cssPath} input`) ||
                           document.querySelector(`${cssPath} .el-input__inner`) ||
                           dateEditor.querySelector('input') ||
                           dateEditor.querySelector('.el-input__inner');

          if (dateInput && dateInput.value && dateInput.value.trim() !== '') {
            Statistics.addLog(`✅ ${fieldName}通过"此刻"按钮设置成功: ${dateInput.value}`);

            // 点击页面其他地方关闭日期选择器
            document.body.click();
            await wait(300);

            return true;
          } else {
            Statistics.addLog(`⚠️ ${fieldName}点击"此刻"按钮后未检测到值变化`);
          }
        } else {
          Statistics.addLog(`❌ 第${attempt}次未找到"此刻"按钮或按钮不可见`);

          // 尝试查找其他可能的"此刻"按钮选择器
          const alternativeSelectors = [
            "button:contains('此刻')",
            ".el-picker-panel__footer button",
            ".el-button--text:contains('此刻')",
            ".el-picker-panel__link-btn"
          ];

          for (const selector of alternativeSelectors) {
            try {
              const buttons = document.querySelectorAll(selector);
              for (const btn of buttons) {
                if (btn.textContent.includes('此刻') || btn.textContent.includes('现在')) {
                  Statistics.addLog(`✅ 通过备用选择器找到"此刻"按钮: ${btn.textContent.trim()}`);
                  btn.click();
                  await wait(800);

                  const dateInput = document.querySelector(`${cssPath} input`) ||
                                   document.querySelector(`${cssPath} .el-input__inner`);

                  if (dateInput && dateInput.value && dateInput.value.trim() !== '') {
                    Statistics.addLog(`✅ ${fieldName}通过备用按钮设置成功: ${dateInput.value}`);
                    document.body.click();
                    await wait(300);
                    return true;
                  }
                }
              }
            } catch (e) {
              // 忽略选择器错误
            }
          }

          // 如果找不到"此刻"按钮，可能需要重新点击输入框
          if (attempt <= 2) {
            Statistics.addLog(`🔄 重新点击${fieldName}输入框`);
            dateInput.click();
            await wait(1000);
          }
        }

      } catch (setError) {
        Statistics.addLog(`❌ ${fieldName}第${attempt}次操作失败: ${setError.message}`);
      }

      if (attempt < 5) {
        await wait(500);
      }
    }

    Statistics.addLog(`❌ ${fieldName}5次尝试均失败`);

    // 确保关闭任何打开的日期选择器
    try {
      document.body.click();
    } catch (e) {}

    return false;

  } catch (error) {
    Statistics.addLog(`${fieldName}处理失败: ${error.message}`, 'error');
    return false;
  }
}

// 根据标签文本查找表单项
async function findFormItemByLabel(labelText) {
  const labels = document.querySelectorAll('label.el-form-item__label');
  for (const label of labels) {
    if (label.textContent.trim() === labelText) {
      return label.closest('.el-form-item');
    }
  }
  return null;
}

// 强制选择特定选项
async function forceSelectSpecificOption(fieldName, selectBox, fieldConfig) {
  try {
    // 关闭所有打开的下拉框
    const openDropdowns = document.querySelectorAll('.el-select-dropdown:not([style*="display: none"])');
    for (const dropdown of openDropdowns) {
      dropdown.style.display = 'none';
    }
    await wait(300);

    // 移除滚动操作，直接处理

    // 特殊处理线索等级字段：使用日期选择同样的直接设置方式
    if (fieldName === '线索等级') {
      return await handleLevelSelectLikeDatePicker(selectBox);
    }

    // 点击选择框
    selectBox.click();
    await wait(getOperationWaitTime());

    // 等待下拉框出现（优化版）
    let dropdown = null;
    for (let i = 0; i < 3; i++) { // 减少重试次数
      const dropdowns = document.querySelectorAll('.el-select-dropdown:not([style*="display: none"])');
      if (dropdowns.length > 0) {
        dropdown = dropdowns[dropdowns.length - 1]; // 取最后一个（最新打开的）
        break;
      }
      await wait(getOperationWaitTime());
    }

    if (!dropdown) {
      Statistics.addLog(`${fieldName} 下拉框未出现`);
      return false;
    }

    const options = dropdown.querySelectorAll('.el-select-dropdown__item');
    Statistics.addLog(`${fieldName} 找到 ${options.length} 个选项`);

    // 查找匹配的选项
    let selectedOption = null;

    // 首先尝试默认选项
    if (fieldConfig.defaultOption) {
      selectedOption = Array.from(options).find(opt =>
        opt.textContent.trim() === fieldConfig.defaultOption
      );
    }

    // 如果没找到默认选项，尝试其他预期选项
    if (!selectedOption && fieldConfig.expectedOptions) {
      for (const expectedOption of fieldConfig.expectedOptions) {
        selectedOption = Array.from(options).find(opt =>
          opt.textContent.trim() === expectedOption
        );
        if (selectedOption) break;
      }
    }

    if (selectedOption) {
      selectedOption.click();
      await wait(getOperationWaitTime());

      // 确保下拉框关闭
      document.body.click();
      await wait(getOperationWaitTime());

      Statistics.addLog(`✅ ${fieldName}设置成功`);
      return true;
    } else {
      // 关闭下拉框
      document.body.click();
      await wait(100); // 减少等待时间
      return false;
    }

  } catch (error) {
    Statistics.addLog(`${fieldName} 选择失败: ${error.message}`, 'error');
    try {
      document.body.click();
    } catch (e) {}
    return false;
  }
}

// 线索等级选择：完全模仿日期选择的直接设置方式
async function handleLevelSelectLikeDatePicker(selectBox) {
  try {
    Statistics.addLog(`🎯 线索等级直接设置（模仿日期选择方式）`);

    // 确定要设置的值
    let targetValue = 'B（30天内跟进）'; // 默认30天

    // 检查是否启用随机选择
    const enableRandomSelect = settings && settings.randomLevelSelect;

    if (enableRandomSelect) {
      // 随机选择模式：从预定义选项中随机选择
      const options = ['B（30天内跟进）', 'A（7天内跟进）', 'H（2天内跟进）'];
      const randomIndex = Math.floor(Math.random() * options.length);
      targetValue = options[randomIndex];
      Statistics.addLog(`🎲 随机选择: ${targetValue}`);
    } else {
      Statistics.addLog(`🎯 设置为30天: ${targetValue}`);
    }

    // 查找输入框（完全模仿日期选择的方式）
    const input = selectBox.querySelector('input') ||
                  selectBox.closest('.el-form-item').querySelector('input') ||
                  selectBox.querySelector('.el-input__inner');

    if (input) {
      // 完全模仿日期选择的设置方式
      input.focus();
      await wait(getOperationWaitTime());

      input.value = targetValue;

      // 触发完全相同的事件序列（和日期选择一样）
      ['focus', 'input', 'change', 'blur'].forEach(eventType => {
        input.dispatchEvent(new Event(eventType, { bubbles: true }));
      });

      await wait(getOperationWaitTime());

      // 验证设置结果（和日期选择一样的验证方式）
      if (input.value === targetValue) {
        Statistics.addLog(`✅ 线索等级直接设置成功: ${targetValue}`);
        return true;
      } else {
        Statistics.addLog(`❌ 线索等级直接设置验证失败`);
        return false;
      }
    } else {
      Statistics.addLog(`❌ 未找到线索等级输入框`);
      return false;
    }

  } catch (error) {
    Statistics.addLog(`线索等级直接设置失败: ${error.message}`, 'error');
    return false;
  }
}

// 丝滑的线索等级直接设置（类似日期选择的方式）
async function handleLevelSelectDirect(selectBox) {
  try {
    Statistics.addLog(`🎯 线索等级直接设置模式`);

    // 确定要设置的值
    let targetValue = 'B（30天内跟进）'; // 默认30天

    // 检查是否启用随机选择
    const enableRandomSelect = settings && settings.randomLevelSelect;

    if (enableRandomSelect) {
      // 随机选择模式：从预定义选项中随机选择
      const options = ['B（30天内跟进）', 'A（7天内跟进）', 'H（2天内跟进）'];
      const randomIndex = Math.floor(Math.random() * options.length);
      targetValue = options[randomIndex];
      Statistics.addLog(`🎲 随机选择: ${targetValue}`);
    } else {
      Statistics.addLog(`🎯 设置为30天: ${targetValue}`);
    }

    // 查找输入框（类似日期选择的方式）
    const input = selectBox.querySelector('input') ||
                  selectBox.closest('.el-form-item').querySelector('input') ||
                  selectBox.querySelector('.el-input__inner');

    if (input) {
      // 直接设置值（类似日期选择的丝滑方式）
      input.focus();
      await wait(50);

      input.value = targetValue;

      // 触发必要的事件
      ['focus', 'input', 'change', 'blur'].forEach(eventType => {
        input.dispatchEvent(new Event(eventType, { bubbles: true }));
      });

      await wait(getOperationWaitTime());

      // 验证设置结果
      if (input.value === targetValue) {
        Statistics.addLog(`✅ 线索等级直接设置成功: ${targetValue}`);
        return true;
      } else {
        Statistics.addLog(`⚠️ 直接设置验证失败，回退到UI操作`);
        return await handleLevelSelect(selectBox);
      }
    } else {
      Statistics.addLog(`❌ 未找到输入框，回退到UI操作`);
      return await handleLevelSelect(selectBox);
    }

  } catch (error) {
    Statistics.addLog(`线索等级直接设置失败: ${error.message}，回退到UI操作`, 'error');
    return await handleLevelSelect(selectBox);
  }
}

// 优化的线索等级选择处理（备用方案）
async function handleLevelSelect(selectBox) {
  try {
    Statistics.addLog(`🎯 处理线索等级字段`);

    // 单次点击，快速响应
    selectBox.click();
    await wait(getOperationWaitTime());

    // 智能等待下拉框出现
    const dropdown = await fastWaitForCondition(() => {
      const dropdowns = document.querySelectorAll('.el-select-dropdown:not([style*="display: none"])');
      return dropdowns.length > 0 ? dropdowns[dropdowns.length - 1] : null;
    }, 30); // 最多等待30次 * 1ms = 30ms

    if (!dropdown) {
      Statistics.addLog(`❌ 线索等级下拉框未出现`);
      return false;
    }

    const options = dropdown.querySelectorAll('.el-select-dropdown__item');
    if (options.length === 0) {
      Statistics.addLog(`❌ 线索等级未找到选项`);
      return false;
    }

    // 过滤有效选项
    const validOptions = Array.from(options).filter(opt => {
      const text = opt.textContent.trim();
      return text !== '' && text !== '请选择' && text !== '全部';
    });

    let selectedOption = null;

    // 检查是否启用随机选择（从设置中获取）
    const enableRandomSelect = settings && settings.randomLevelSelect;

    if (enableRandomSelect && validOptions.length > 0) {
      // 随机选择模式
      const randomIndex = Math.floor(Math.random() * validOptions.length);
      selectedOption = validOptions[randomIndex];
      Statistics.addLog(`🎲 随机选择: ${selectedOption.textContent.trim()}`);
    } else {
      // 非随机模式：确保选择30天的选项
      // 优先查找包含"B"和"30天"的选项
      selectedOption = validOptions.find(opt => {
        const text = opt.textContent.trim();
        return text.includes('B') && text.includes('30天');
      });

      // 如果没找到B（30天），查找任何包含"30天"的选项
      if (!selectedOption) {
        selectedOption = validOptions.find(opt => {
          const text = opt.textContent.trim();
          return text.includes('30天');
        });
      }

      // 如果还没找到30天的选项，选择第一个有效选项
      if (!selectedOption && validOptions.length > 0) {
        selectedOption = validOptions[0];
        Statistics.addLog(`⚠️ 未找到30天选项，选择: ${selectedOption.textContent.trim()}`);
      }
    }

    if (selectedOption) {
      selectedOption.click();
      await wait(getOperationWaitTime());

      // 关闭下拉框
      document.body.click();
      await wait(getOperationWaitTime());

      Statistics.addLog(`✅ 线索等级设置成功: ${selectedOption.textContent.trim()}`);
      return true;
    } else {
      Statistics.addLog(`❌ 线索等级未找到合适选项`);
      return false;
    }

  } catch (error) {
    Statistics.addLog(`线索等级处理失败: ${error.message}`, 'error');
    return false;
  }
}



// 强制填充日期
async function forceFillDate(fieldName, dateEditor) {
  try {
    Statistics.addLog(`📅 开始填充日期字段: ${fieldName}`);

    const input = dateEditor.querySelector('input');
    if (!input) {
      Statistics.addLog(`❌ ${fieldName} 未找到日期输入框`);
      return false;
    }

    // 检查是否已有值
    if (input.value && input.value.trim() !== '') {
      Statistics.addLog(`📅 ${fieldName} 已有值: ${input.value}，强制重新填充`);
    }

    // 滚动到位置
    input.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await wait(400);

    // 多次尝试激活输入框
    for (let i = 0; i < 3; i++) {
      input.click();
      await wait(300);
      input.focus();
      await wait(200);
    }

    let dateString = '';
    if (fieldName === '预购日期') {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);
      futureDate.setHours(10 + Math.floor(Math.random() * 8));
      futureDate.setMinutes(Math.floor(Math.random() * 60));

      const year = futureDate.getFullYear();
      const month = String(futureDate.getMonth() + 1).padStart(2, '0');
      const day = String(futureDate.getDate()).padStart(2, '0');
      const hour = String(futureDate.getHours()).padStart(2, '0');
      const minute = String(futureDate.getMinutes()).padStart(2, '0');
      dateString = `${year}-${month}-${day} ${hour}:${minute}`;
    } else if (fieldName === '计划跟进时间') {
      const followDate = new Date();
      followDate.setDate(followDate.getDate() + Math.floor(Math.random() * 3) + 1);
      followDate.setHours(9 + Math.floor(Math.random() * 9));
      followDate.setMinutes(Math.floor(Math.random() * 60));

      const year = followDate.getFullYear();
      const month = String(followDate.getMonth() + 1).padStart(2, '0');
      const day = String(followDate.getDate()).padStart(2, '0');
      const hour = String(followDate.getHours()).padStart(2, '0');
      const minute = String(followDate.getMinutes()).padStart(2, '0');
      dateString = `${year}-${month}-${day} ${hour}:${minute}`;
    }

    if (dateString) {
      Statistics.addLog(`📅 准备设置日期: ${dateString}`);

      // 多种方法尝试设置值
      try {
        // 方法1: 直接设置值
        input.value = '';
        await wait(100);
        input.value = dateString;

        // 方法2: 模拟用户输入
        input.dispatchEvent(new Event('focus', { bubbles: true }));
        await wait(100);
        input.dispatchEvent(new Event('input', { bubbles: true }));
        await wait(100);
        input.dispatchEvent(new Event('change', { bubbles: true }));
        await wait(100);
        input.dispatchEvent(new Event('blur', { bubbles: true }));

        // 方法3: 触发Vue事件（如果是Vue组件）
        if (input.__vue__) {
          input.__vue__.$emit('input', dateString);
          input.__vue__.$emit('change', dateString);
        }

        await wait(300);

        // 验证是否设置成功
        if (input.value === dateString) {
          Statistics.addLog(`✅ ${fieldName} 日期设置成功: ${dateString}`);
          return true;
        } else {
          Statistics.addLog(`⚠️ ${fieldName} 日期设置后验证失败，当前值: ${input.value}`);

          // 再次尝试设置
          input.value = dateString;
          input.dispatchEvent(new Event('input', { bubbles: true }));
          input.dispatchEvent(new Event('change', { bubbles: true }));
          await wait(200);

          if (input.value === dateString) {
            Statistics.addLog(`✅ ${fieldName} 第二次尝试成功: ${dateString}`);
            return true;
          }
        }

      } catch (setError) {
        Statistics.addLog(`❌ ${fieldName} 设置日期时出错: ${setError.message}`);
      }
    }

    Statistics.addLog(`❌ ${fieldName} 日期填充失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ 填充日期${fieldName}失败: ${error.message}`, 'error');
    return false;
  }
}

// 填充日期字段
async function fillDateFields() {
  try {
    Statistics.addLog('📅 开始填充日期字段');

    // 查找所有日期输入框
    const dateFields = document.querySelectorAll('.el-date-editor input');
    Statistics.addLog(`📅 找到 ${dateFields.length} 个日期字段`);

    for (const dateField of dateFields) {
      if (dateField.value && dateField.value.trim() !== '') {
        Statistics.addLog(`📅 日期字段已有值，跳过: ${dateField.value}`);
        continue;
      }

      const formItem = dateField.closest('.el-form-item');
      const label = formItem?.querySelector('label');
      const fieldName = label?.textContent.trim();

      if (!fieldName) {
        Statistics.addLog('📅 未找到日期字段标签');
        continue;
      }

      // 滚动到字段位置
      dateField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      await wait(300);

      if (fieldName === '预购日期') {
        // 设置为未来7-30天的随机日期
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);
        futureDate.setHours(10 + Math.floor(Math.random() * 8)); // 10-17点
        futureDate.setMinutes(Math.floor(Math.random() * 60));

        const year = futureDate.getFullYear();
        const month = String(futureDate.getMonth() + 1).padStart(2, '0');
        const day = String(futureDate.getDate()).padStart(2, '0');
        const hour = String(futureDate.getHours()).padStart(2, '0');
        const minute = String(futureDate.getMinutes()).padStart(2, '0');
        const dateString = `${year}-${month}-${day} ${hour}:${minute}`;

        // 点击输入框激活
        dateField.click();
        await wait(200);

        // 设置值
        dateField.value = dateString;
        dateField.dispatchEvent(new Event('input', { bubbles: true }));
        dateField.dispatchEvent(new Event('change', { bubbles: true }));
        dateField.dispatchEvent(new Event('blur', { bubbles: true }));

        Statistics.addLog(`✓ ${fieldName}: ${dateString}`);
        await wait(300);

      } else if (fieldName === '计划跟进时间') {
        // 设置为未来1-3天的随机时间
        const followDate = new Date();
        followDate.setDate(followDate.getDate() + Math.floor(Math.random() * 3) + 1);
        followDate.setHours(9 + Math.floor(Math.random() * 9)); // 9-17点
        followDate.setMinutes(Math.floor(Math.random() * 60));

        const year = followDate.getFullYear();
        const month = String(followDate.getMonth() + 1).padStart(2, '0');
        const day = String(followDate.getDate()).padStart(2, '0');
        const hour = String(followDate.getHours()).padStart(2, '0');
        const minute = String(followDate.getMinutes()).padStart(2, '0');
        const dateString = `${year}-${month}-${day} ${hour}:${minute}`;

        // 点击输入框激活
        dateField.click();
        await wait(200);

        // 设置值
        dateField.value = dateString;
        dateField.dispatchEvent(new Event('input', { bubbles: true }));
        dateField.dispatchEvent(new Event('change', { bubbles: true }));
        dateField.dispatchEvent(new Event('blur', { bubbles: true }));

        Statistics.addLog(`✓ ${fieldName}: ${dateString}`);
        await wait(300);
      }
    }
  } catch (error) {
    Statistics.addLog(`填充日期字段失败: ${error.message}`, 'error');
  }
}

// 填充文本区域
async function fillTextAreas() {
  try {
    const textAreas = document.querySelectorAll('textarea');

    for (const textArea of textAreas) {
      if (textArea.value) continue; // 跳过已有值的字段

      const label = textArea.closest('.el-form-item')?.querySelector('label');
      const fieldName = label?.textContent.trim();

      if (fieldName === '跟进说明') {
        const remarks = [
          '客户对车型表现出浓厚兴趣，计划近期到店详细了解',
          '已向客户介绍车型基本信息，客户反馈良好',
          '客户询问了优惠政策和金融方案，需要进一步跟进',
          '客户表示需要和家人商量，约定下次联系时间',
          '已发送车型详细资料给客户，等待客户反馈'
        ];

        const randomRemark = remarks[Math.floor(Math.random() * remarks.length)];
        textArea.value = randomRemark;
        textArea.dispatchEvent(new Event('input', { bubbles: true }));
        textArea.dispatchEvent(new Event('change', { bubbles: true }));

        Statistics.addLog(`✓ ${fieldName}: ${randomRemark}`);
        await wait(200);
      }
    }
  } catch (error) {
    Statistics.addLog(`填充文本区域失败: ${error.message}`, 'error');
  }
}

// 处理车型选择框
async function handleCarTypeSelect() {
  return handleSelect('车型', "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(2) > div:nth-child(1) > div > div > div > div.el-input.el-input--small.el-input--suffix");
}

// 处理真实性选择框
async function handleRealitySelect() {
  return handleSelect('真实性', "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(4) > div:nth-child(4) > div > div > div > div.el-input.el-input--small.el-input--suffix");
}

// 专门处理Element UI日期选择器的函数
async function handleElementUIDatePicker(cssPath, fieldName) {
  try {
    const element = document.querySelector(cssPath);
    if (!element) {
      Statistics.addLog(`❌ 未找到${fieldName}元素: ${cssPath}`);
      return false;
    }

    // 检查是否启用随机选择（仅针对计划跟进时间）
    const enableRandomSelect = (fieldName === '计划跟进时间' && settings && settings.randomFollowTime);

    if (enableRandomSelect) {
      Statistics.addLog(`🎲 ${fieldName}启用随机选择模式`);
      Statistics.addLog(`🎯 直接使用智能随机设置，跳过面板操作`);

      // 直接使用智能随机设置，跳过复杂的面板操作
      const dateInput = element.querySelector('input') || element.closest('.el-form-item').querySelector('input');
      if (dateInput) {
        return await tryRandomSimpleDateSet(dateInput, fieldName);
      } else {
        Statistics.addLog(`❌ 未找到${fieldName}的输入框`);
        return false;
      }
    }

    // 如果找到的是label，需要找到对应的日期编辑器
    let dateEditor = null;
    if (element.tagName === 'LABEL') {
      // 通过label找到对应的表单项
      const formItem = element.closest('.el-form-item');
      if (formItem) {
        dateEditor = formItem.querySelector('.el-date-editor');
      }
    } else {
      dateEditor = element.querySelector('.el-date-editor') || element.closest('.el-date-editor') || element;
    }

    if (!dateEditor) {
      Statistics.addLog(`❌ ${fieldName}中未找到日期编辑器`);
      return false;
    }

    // 查找输入框
    const dateInput = dateEditor.querySelector('input');
    if (!dateInput) {
      Statistics.addLog(`❌ ${fieldName}日期编辑器中未找到输入框`);
      return false;
    }

    // 首先尝试多种方式打开日期选择器
    const pickerOpened = await tryOpenDatePicker(dateEditor, dateInput, fieldName);
    if (!pickerOpened) {
      Statistics.addLog(`❌ ${fieldName}无法打开日期选择器，尝试简单设置`);
      return await trySimpleDateSet(dateInput, fieldName);
    }

    // 方法1: 尝试通过"此刻"按钮设置（最可靠）
    const nowButtonSuccess = await tryNowButton(dateInput, fieldName);
    if (nowButtonSuccess) {
      return true;
    }

    // 方法2: 尝试通过日期选择器面板设置
    const panelSuccess = await tryDatePickerPanel(dateInput, fieldName);
    if (panelSuccess) {
      return true;
    }

    // 方法3: 尝试简单设置（最后手段）
    const simpleSuccess = await trySimpleDateSet(dateInput, fieldName);
    if (simpleSuccess) {
      return true;
    }

    Statistics.addLog(`❌ ${fieldName}所有方法均失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`${fieldName}处理失败: ${error.message}`, 'error');
    return false;
  }
}

// 处理随机日期时间选择器
async function handleRandomDateTimePicker(element, fieldName) {
  try {
    Statistics.addLog(`🎲 开始随机选择${fieldName}`);

    // 如果找到的是label，需要找到对应的日期编辑器
    let dateEditor = null;
    if (element.tagName === 'LABEL') {
      const formItem = element.closest('.el-form-item');
      if (formItem) {
        dateEditor = formItem.querySelector('.el-date-editor');
      }
    } else {
      dateEditor = element.querySelector('.el-date-editor') || element.closest('.el-date-editor') || element;
    }

    if (!dateEditor) {
      Statistics.addLog(`❌ ${fieldName}中未找到日期编辑器`);
      return false;
    }

    // 查找输入框
    const dateInput = dateEditor.querySelector('input');
    if (!dateInput) {
      Statistics.addLog(`❌ ${fieldName}日期编辑器中未找到输入框`);
      return false;
    }

    // 点击输入框打开日期选择器
    dateInput.click();
    await wait(getOperationWaitTime());

    // 使用智能检测等待日期选择器面板出现
    const pickerPanel = await waitForDatePickerPanelWithAutoDetection();
    if (!pickerPanel) {
      Statistics.addLog(`❌ ${fieldName}日期选择器面板未出现，尝试简单设置`);
      return await trySimpleDateSet(dateInput, fieldName);
    }

    // 随机选择可用日期和时间
    const success = await selectRandomDateTimeWithAutoDetection(pickerPanel, dateInput, fieldName);
    if (success) {
      return true;
    }

    // 如果随机选择失败，尝试简单设置
    Statistics.addLog(`❌ ${fieldName}随机选择失败，尝试简单设置`);
    return await trySimpleDateSet(dateInput, fieldName);

  } catch (error) {
    Statistics.addLog(`${fieldName}随机选择失败: ${error.message}`, 'error');
    return false;
  }
}

// 简化的随机日期时间处理器（直接使用简单设置）
async function handleRandomDateTimeSimple(element, fieldName) {
  try {
    Statistics.addLog(`🎲 开始简化随机选择${fieldName}`);

    // 如果找到的是label，需要找到对应的日期编辑器
    let dateEditor = null;
    if (element.tagName === 'LABEL') {
      const formItem = element.closest('.el-form-item');
      if (formItem) {
        dateEditor = formItem.querySelector('.el-date-editor');
      }
    } else {
      dateEditor = element.querySelector('.el-date-editor') || element.closest('.el-date-editor') || element;
    }

    if (!dateEditor) {
      Statistics.addLog(`❌ ${fieldName}中未找到日期编辑器`);
      return false;
    }

    // 查找输入框
    const dateInput = dateEditor.querySelector('input');
    if (!dateInput) {
      Statistics.addLog(`❌ ${fieldName}日期编辑器中未找到输入框`);
      return false;
    }

    // 直接使用简单设置方法，生成随机日期时间
    return await tryRandomSimpleDateSet(dateInput, fieldName);

  } catch (error) {
    Statistics.addLog(`${fieldName}简化随机选择失败: ${error.message}`, 'error');
    return false;
  }
}

// 简化的随机日期选择处理器（只选择日期，不操作时间）
async function handleRandomDateOnlySelection(element, fieldName) {
  try {
    Statistics.addLog(`🎲 开始随机选择${fieldName}（仅日期）`);

    // 首先关闭可能已经打开的日期选择器
    document.body.click();
    await wait(getOperationWaitTime());

    // 如果找到的是label，需要找到对应的日期编辑器
    let dateEditor = null;
    if (element.tagName === 'LABEL') {
      const formItem = element.closest('.el-form-item');
      if (formItem) {
        dateEditor = formItem.querySelector('.el-date-editor');
      }
    } else {
      dateEditor = element.querySelector('.el-date-editor') || element.closest('.el-date-editor') || element;
    }

    if (!dateEditor) {
      Statistics.addLog(`❌ ${fieldName}中未找到日期编辑器`);
      return false;
    }

    // 查找输入框
    const dateInput = dateEditor.querySelector('input');
    if (!dateInput) {
      Statistics.addLog(`❌ ${fieldName}日期编辑器中未找到输入框`);
      return false;
    }

    // 使用简化的随机日期选择方法
    return await selectRandomDateOnly(dateInput, fieldName);

  } catch (error) {
    Statistics.addLog(`${fieldName}随机日期选择失败: ${error.message}`, 'error');
    return false;
  }
}

// 简化的随机日期选择（只选择日期，不操作时间）
async function selectRandomDateOnly(dateInput, fieldName) {
  try {
    Statistics.addLog(`🎲 开始随机选择日期（基于ys文件结构分析）`);

    // 1. 多种方式尝试打开日期选择器（基于ys文件分析）
    Statistics.addLog(`🖱️ 尝试多种方式打开日期选择器`);

    // 方法1: 点击日期编辑器容器
    const dateEditor = dateInput.closest('.el-date-editor');
    if (dateEditor) {
      Statistics.addLog(`🖱️ 方法1: 点击日期编辑器容器`);
      dateEditor.click();
      await wait(getOperationWaitTime());

      // 立即检查是否有面板出现
      let quickCheck = document.querySelector('.el-picker-panel.el-date-picker');
      if (quickCheck) {
        Statistics.addLog(`✅ 方法1成功，面板已出现`);
      } else {
        Statistics.addLog(`❌ 方法1失败，尝试方法2`);

        // 方法2: 触发focus和click事件
        dateInput.focus();
        await wait(100);
        dateInput.click();
        await wait(getOperationWaitTime());

        quickCheck = document.querySelector('.el-picker-panel.el-date-picker');
        if (quickCheck) {
          Statistics.addLog(`✅ 方法2成功，面板已出现`);
        } else {
          Statistics.addLog(`❌ 方法2失败，尝试方法3`);

          // 方法3: 模拟鼠标事件
          const mouseEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window
          });
          dateEditor.dispatchEvent(mouseEvent);
          await wait(getOperationWaitTime());

          quickCheck = document.querySelector('.el-picker-panel.el-date-picker');
          if (quickCheck) {
            Statistics.addLog(`✅ 方法3成功，面板已出现`);
          } else {
            Statistics.addLog(`❌ 所有方法都失败，继续等待面板出现`);
          }
        }
      }
    } else {
      Statistics.addLog(`❌ 未找到日期编辑器容器，直接点击输入框`);
      dateInput.click();
      await wait(getOperationWaitTime());
    }

    // 2. 等待日期选择器面板出现
    const pickerPanel = await waitForDatePickerPanelWithAutoDetection();
    if (!pickerPanel) {
      Statistics.addLog(`❌ ${fieldName}日期选择器面板未出现，使用智能随机设置`);
      Statistics.addLog(`🎯 智能随机设置将从可用日期中选择，然后直接设置到输入框`);
      return await tryRandomSimpleDateSet(dateInput, fieldName);
    }

    Statistics.addLog(`✅ 找到日期选择器面板`);

    // 3. 查找所有可用日期（基于ys文件分析的精确结构）
    const availableDates = pickerPanel.querySelectorAll('.el-date-table td.available:not(.disabled)');

    if (availableDates.length === 0) {
      Statistics.addLog(`❌ 未找到可用日期，尝试其他选择器`);
      // 尝试备用选择器
      const backupDates = pickerPanel.querySelectorAll('.el-date-table td:not(.disabled):not(.prev-month):not(.next-month)');
      if (backupDates.length > 0) {
        Statistics.addLog(`🔍 备用选择器找到 ${backupDates.length} 个可用日期`);
        // 使用备用日期列表
        const randomIndex = Math.floor(Math.random() * backupDates.length);
        const selectedDate = backupDates[randomIndex];
        const dateSpan = selectedDate.querySelector('span');
        const dateText = dateSpan ? dateSpan.textContent.trim() : '未知';

        Statistics.addLog(`🎲 备用方案随机选择: ${dateText}号`);
        selectedDate.click();
        await wait(getOperationWaitTime());

        // 继续到确定按钮步骤
      } else {
        Statistics.addLog(`❌ 备用选择器也未找到可用日期，回退到简单设置`);
        document.body.click();
        await wait(getOperationWaitTime());
        return await tryRandomSimpleDateSet(dateInput, fieldName);
      }
    } else {
      Statistics.addLog(`🔍 找到 ${availableDates.length} 个可用日期`);

      // 4. 随机选择一个可用日期
      const randomIndex = Math.floor(Math.random() * availableDates.length);
      const selectedDate = availableDates[randomIndex];
      const dateSpan = selectedDate.querySelector('span');
      const dateText = dateSpan ? dateSpan.textContent.trim() : '未知';

      Statistics.addLog(`🎲 随机选择第 ${randomIndex + 1} 个日期: ${dateText}号`);

      // 5. 点击选中的日期
      selectedDate.click();
      await wait(getOperationWaitTime());
    }

    // 4. 随机选择一个可用日期
    const randomIndex = Math.floor(Math.random() * availableDates.length);
    const selectedDate = availableDates[randomIndex];
    const dateSpan = selectedDate.querySelector('span');
    const dateText = dateSpan ? dateSpan.textContent.trim() : '未知';

    Statistics.addLog(`🎲 随机选择第 ${randomIndex + 1} 个日期: ${dateText}号`);

    // 5. 点击选中的日期
    selectedDate.click();
    await wait(getOperationWaitTime());

    // 6. 查找并点击确定按钮（基于ys文件的精确结构）
    let confirmButton = null;

    // 方法1：标准选择器
    confirmButton = pickerPanel.querySelector('.el-picker-panel__footer .el-button--default');

    // 方法2：通过文本内容查找
    if (!confirmButton) {
      const buttons = pickerPanel.querySelectorAll('.el-picker-panel__footer button');
      for (const btn of buttons) {
        if (btn.textContent && btn.textContent.includes('确定')) {
          confirmButton = btn;
          break;
        }
      }
    }

    // 方法3：通过类名查找
    if (!confirmButton) {
      confirmButton = pickerPanel.querySelector('.el-picker-panel__footer .el-button.el-picker-panel__link-btn.el-button--default');
    }

    if (confirmButton) {
      Statistics.addLog(`🖱️ 找到确定按钮，准备点击`);

      // 记录点击前的值
      const beforeValue = dateInput.value;
      Statistics.addLog(`📝 点击确定前的值: "${beforeValue}"`);

      confirmButton.click();

      // 等待更长时间让Element UI更新值
      await wait(getOperationWaitTime() * 2);

      // 使用智能等待，确保值已更新
      let valueUpdated = false;
      for (let i = 0; i < 10; i++) {
        const currentValue = dateInput.value;
        if (currentValue && currentValue.trim() !== '' && currentValue !== beforeValue) {
          valueUpdated = true;
          Statistics.addLog(`✅ 检测到值已更新: "${currentValue}"`);
          break;
        }
        await wait(100); // 每100ms检查一次
      }

      // 7. 验证设置结果
      const finalValue = dateInput.value;
      if (finalValue && finalValue.trim() !== '') {
        Statistics.addLog(`✅ ${fieldName}随机日期选择成功: ${finalValue}`);

        // 额外验证：检查日期是否包含我们选择的日期
        if (finalValue.includes(`${dateText}`)) {
          Statistics.addLog(`✅ 确认选择的日期${dateText}号已正确设置`);
        } else {
          Statistics.addLog(`⚠️ 最终值与选择的日期不匹配，但有值: ${finalValue}`);
        }
        return true;
      } else {
        Statistics.addLog(`❌ ${fieldName}设置后输入框仍为空`);
        Statistics.addLog(`🔍 调试：确定按钮点击后值未更新`);
      }
    } else {
      Statistics.addLog(`❌ 未找到确定按钮，尝试"此刻"按钮`);

      // 尝试点击"此刻"按钮作为备用方案
      const nowButton = pickerPanel.querySelector('.el-picker-panel__footer .el-button--text');
      if (nowButton && nowButton.textContent.includes('此刻')) {
        Statistics.addLog(`🖱️ 点击"此刻"按钮作为备用方案`);
        nowButton.click();
        await wait(getOperationWaitTime());

        if (dateInput.value && dateInput.value.trim() !== '') {
          Statistics.addLog(`✅ ${fieldName}"此刻"按钮设置成功: ${dateInput.value}`);
          return true;
        }
      }
    }

    // 8. 如果上述步骤失败，尝试关闭面板并回退
    document.body.click();
    await wait(getOperationWaitTime());
    return false;

  } catch (error) {
    Statistics.addLog(`随机日期选择出错: ${error.message}`, 'error');
    // 出错时回退到简单设置
    document.body.click();
    await wait(getOperationWaitTime());
    return await tryRandomSimpleDateSet(dateInput, fieldName);
  }
}

// 智能随机日期设置（从可用日期中选择）
async function tryRandomSimpleDateSet(dateInput, fieldName) {
  try {
    Statistics.addLog(`🎲 智能生成随机${fieldName}（从可用日期选择）`);

    // 首先尝试获取可用日期（多次重试确保成功）
    let availableDates = [];

    // 多次尝试获取可用日期
    for (let attempt = 1; attempt <= 3; attempt++) {
      Statistics.addLog(`🔍 第${attempt}次尝试获取可用日期`);

      // 尝试打开日期选择器获取可用日期
      const dateEditor = dateInput.closest('.el-date-editor');
      if (dateEditor) {
        dateEditor.click();
        await wait(300); // 增加等待时间

        const panel = document.querySelector('.el-picker-panel.el-date-picker');
        if (panel && panel.querySelector('.el-date-table')) {
          const availableDateElements = panel.querySelectorAll('.el-date-table td.available:not(.disabled)');

          if (availableDateElements.length > 0) {
            Statistics.addLog(`✅ 第${attempt}次尝试成功：获取到 ${availableDateElements.length} 个可用日期`);

            // 提取可用日期
            availableDateElements.forEach(dateElement => {
              const span = dateElement.querySelector('span');
              if (span) {
                const dayNumber = parseInt(span.textContent.trim());
                if (!isNaN(dayNumber)) {
                  availableDates.push(dayNumber);
                }
              }
            });

            // 关闭面板
            document.body.click();
            await wait(200);
            break; // 成功获取，退出重试循环
          } else {
            Statistics.addLog(`❌ 第${attempt}次尝试失败：未找到可用日期元素`);
          }
        } else {
          Statistics.addLog(`❌ 第${attempt}次尝试失败：未找到日期面板`);
        }

        // 关闭可能打开的面板
        document.body.click();
        await wait(200);
      } else {
        Statistics.addLog(`❌ 第${attempt}次尝试失败：未找到日期编辑器`);
      }
    }

    // 生成随机日期时间
    const futureDate = new Date();

    if (availableDates.length > 0) {
      // 从可用日期中随机选择
      const randomDay = availableDates[Math.floor(Math.random() * availableDates.length)];

      // 设置为当前月的该日期
      futureDate.setDate(randomDay);

      // 如果选择的日期在过去，则设置为下个月
      if (futureDate < new Date()) {
        futureDate.setMonth(futureDate.getMonth() + 1);
        futureDate.setDate(randomDay);
      }

      Statistics.addLog(`✅ 从可用日期中随机选择: ${randomDay}号`);
      Statistics.addLog(`🎯 可用日期列表: [${availableDates.join(', ')}]`);
    } else {
      // 备用方案：随机选择未来7-30天
      const randomDays = Math.floor(Math.random() * 23) + 7;
      futureDate.setDate(futureDate.getDate() + randomDays);
      Statistics.addLog(`⚠️ 警告：无法获取可用日期，使用备用方案`);
      Statistics.addLog(`🎲 备用方案：随机选择未来 ${randomDays} 天`);
      Statistics.addLog(`❗ 注意：此日期可能不在系统允许的可用日期范围内`);
    }

    // 随机选择工作时间9-17点
    const randomHour = 9 + Math.floor(Math.random() * 9);
    const randomMinute = Math.floor(Math.random() * 60);

    futureDate.setHours(randomHour);
    futureDate.setMinutes(randomMinute);
    futureDate.setSeconds(0);

    const year = futureDate.getFullYear();
    const month = String(futureDate.getMonth() + 1).padStart(2, '0');
    const day = String(futureDate.getDate()).padStart(2, '0');
    const hour = String(futureDate.getHours()).padStart(2, '0');
    const minute = String(futureDate.getMinutes()).padStart(2, '0');

    const dateString = `${year}-${month}-${day} ${hour}:${minute}:00`;

    Statistics.addLog(`🎲 最终生成时间: ${dateString}`);

    // 设置到输入框
    dateInput.focus();
    await wait(getOperationWaitTime());

    dateInput.value = dateString;

    // 触发事件
    ['focus', 'input', 'change', 'blur'].forEach(eventType => {
      dateInput.dispatchEvent(new Event(eventType, { bubbles: true }));
    });

    await wait(getOperationWaitTime());

    // 验证设置结果
    if (dateInput.value && dateInput.value.includes(dateString.substring(0, 10))) {
      Statistics.addLog(`✅ ${fieldName}智能随机设置成功: ${dateInput.value}`);
      return true;
    } else {
      Statistics.addLog(`❌ ${fieldName}智能随机设置验证失败`);
      return false;
    }

  } catch (error) {
    Statistics.addLog(`${fieldName}随机简单设置失败: ${error.message}`, 'error');
    return false;
  }
}

// 使用自动检测等待日期选择器面板出现（基于ys文件分析优化）
async function waitForDatePickerPanelWithAutoDetection() {
  Statistics.addLog(`🔍 等待日期选择器面板出现...`);

  // 基于ys文件的实际结构，使用更精确的选择器
  const result = await fastWaitForCondition(() => {
    // 尝试多种选择器，确保能找到面板
    let panel = null;

    // 方法1：标准选择器
    const panels1 = document.querySelectorAll('.el-picker-panel.el-date-picker.el-popper.has-time');
    if (panels1.length > 0) {
      panel = panels1[panels1.length - 1];
      if (panel && !panel.style.display.includes('none')) {
        Statistics.addLog(`✅ 方法1找到日期选择器面板`);
        return panel;
      }
    }

    // 方法2：不检查display属性
    const panels2 = document.querySelectorAll('.el-picker-panel.el-date-picker');
    if (panels2.length > 0) {
      panel = panels2[panels2.length - 1];
      // 检查面板是否真的可见（通过检查是否有日期表格）
      if (panel && panel.querySelector('.el-date-table')) {
        Statistics.addLog(`✅ 方法2找到日期选择器面板`);
        return panel;
      }
    }

    // 方法3：通过z-index检查（基于ys文件中的z-index: 1003）
    const panels3 = document.querySelectorAll('.el-picker-panel');
    for (const p of panels3) {
      if (p.style.zIndex && parseInt(p.style.zIndex) >= 1000 && p.querySelector('.el-date-table')) {
        Statistics.addLog(`✅ 方法3找到日期选择器面板，z-index: ${p.style.zIndex}`);
        return p;
      }
    }

    // 方法4：检查所有面板，不管z-index
    const panels4 = document.querySelectorAll('.el-picker-panel');
    for (const p of panels4) {
      if (p.querySelector('.el-date-table') && p.offsetWidth > 0 && p.offsetHeight > 0) {
        Statistics.addLog(`✅ 方法4找到可见的日期选择器面板`);
        return p;
      }
    }

    return null;
  }, 5000); // 增加等待时间到5秒

  if (!result) {
    Statistics.addLog(`❌ 等待5秒后仍未找到日期选择器面板`);
    // 调试信息：列出所有可能的面板
    const allPanels = document.querySelectorAll('.el-picker-panel');
    Statistics.addLog(`🔍 调试：找到 ${allPanels.length} 个picker面板`);
    allPanels.forEach((panel, index) => {
      Statistics.addLog(`🔍 面板${index}: ${panel.className}, display: ${panel.style.display}, zIndex: ${panel.style.zIndex}`);
    });
  }

  return result;
}

// 快速等待日期选择器面板出现
async function fastWaitForDatePickerPanel() {
  // 使用快速检测机制，1毫秒间隔检测，最多等待2秒
  return await fastWaitForCondition(() => {
    const panels = document.querySelectorAll('.el-picker-panel.el-date-picker:not([style*="display: none"])');
    return panels.length > 0 ? panels[panels.length - 1] : null;
  }, 2000);
}

// 等待日期选择器面板出现（备用函数）
async function waitForDatePickerPanel() {
  for (let i = 0; i < 10; i++) {
    const panels = document.querySelectorAll('.el-picker-panel.el-date-picker:not([style*="display: none"])');
    if (panels.length > 0) {
      return panels[panels.length - 1];
    }
    await wait(300);
  }
  return null;
}

// 使用自动检测的随机选择日期和时间
async function selectRandomDateTimeWithAutoDetection(pickerPanel, dateInput, fieldName) {
  try {
    Statistics.addLog(`🎲 开始随机选择日期和时间`);

    // 检查pickerPanel是否有效
    if (!pickerPanel || typeof pickerPanel.querySelector !== 'function') {
      Statistics.addLog(`❌ 日期选择器面板无效`);
      return false;
    }

    // 1. 随机选择可用日期
    const dateSuccess = await selectRandomDateWithAutoDetection(pickerPanel);
    if (!dateSuccess) {
      Statistics.addLog(`❌ 随机选择日期失败`);
      return false;
    }

    // 2. 随机选择时间
    const timeSuccess = await selectRandomTimeWithAutoDetection(pickerPanel);
    if (!timeSuccess) {
      Statistics.addLog(`⚠️ 随机选择时间失败，使用默认时间`);
    }

    // 3. 点击确定按钮并等待结果
    const confirmButton = pickerPanel.querySelector('.el-picker-panel__footer .el-button--default');
    if (confirmButton) {
      confirmButton.click();
      await wait(getOperationWaitTime());

      // 验证设置结果
      if (dateInput.value && dateInput.value.trim() !== '') {
        Statistics.addLog(`✅ ${fieldName}随机选择成功: ${dateInput.value}`);

        // 关闭日期选择器
        document.body.click();
        await wait(getOperationWaitTime());

        return true;
      }
    }

    Statistics.addLog(`❌ ${fieldName}确定按钮点击失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`随机选择日期时间出错: ${error.message}`, 'error');
    return false;
  }
}

// 随机选择日期和时间（原版本保留）
async function selectRandomDateTime(pickerPanel, dateInput, fieldName) {
  try {
    Statistics.addLog(`🎲 开始随机选择日期和时间`);

    // 1. 随机选择可用日期
    const dateSuccess = await selectRandomDate(pickerPanel);
    if (!dateSuccess) {
      Statistics.addLog(`❌ 随机选择日期失败`);
      return false;
    }

    // 短暂等待日期选择生效
    await wait(100);

    // 2. 随机选择时间
    const timeSuccess = await selectRandomTime(pickerPanel);
    if (!timeSuccess) {
      Statistics.addLog(`⚠️ 随机选择时间失败，使用默认时间`);
    }

    // 短暂等待时间设置生效
    await wait(100);

    // 3. 点击确定按钮
    const confirmButton = pickerPanel.querySelector('.el-picker-panel__footer .el-button--default');
    if (confirmButton) {
      confirmButton.click();

      // 使用快速检测等待结果
      await fastWaitForCondition(() => {
        return dateInput.value && dateInput.value.trim() !== '';
      }, 1000);

      // 验证设置结果
      if (dateInput.value && dateInput.value.trim() !== '') {
        Statistics.addLog(`✅ ${fieldName}随机选择成功: ${dateInput.value}`);
        return true;
      }
    }

    Statistics.addLog(`❌ ${fieldName}确定按钮点击失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`随机选择日期时间出错: ${error.message}`, 'error');
    return false;
  }
}

// 使用自动检测的随机选择可用日期
async function selectRandomDateWithAutoDetection(pickerPanel) {
  try {
    // 检查pickerPanel是否有效
    if (!pickerPanel || typeof pickerPanel.querySelectorAll !== 'function') {
      Statistics.addLog(`❌ 日期选择器面板无效`);
      return false;
    }

    // 查找所有可用日期（不包括禁用的日期）
    const availableDates = pickerPanel.querySelectorAll('.el-date-table td.available:not(.disabled)');

    if (availableDates.length === 0) {
      Statistics.addLog(`❌ 未找到可用日期`);
      return false;
    }

    // 随机选择一个可用日期
    const randomIndex = Math.floor(Math.random() * availableDates.length);
    const selectedDate = availableDates[randomIndex];
    const dateText = selectedDate.querySelector('span').textContent.trim();

    Statistics.addLog(`🎲 随机选择日期: ${dateText}号 (${randomIndex + 1}/${availableDates.length})`);

    selectedDate.click();
    await wait(getOperationWaitTime()); // 使用动态等待时间

    return true;

  } catch (error) {
    Statistics.addLog(`随机选择日期出错: ${error.message}`, 'error');
    return false;
  }
}

// 随机选择可用日期（原版本保留）
async function selectRandomDate(pickerPanel) {
  try {
    // 查找所有可用日期（不包括禁用的日期）
    const availableDates = pickerPanel.querySelectorAll('.el-date-table td.available:not(.disabled)');

    if (availableDates.length === 0) {
      Statistics.addLog(`❌ 未找到可用日期`);
      return false;
    }

    // 随机选择一个可用日期
    const randomIndex = Math.floor(Math.random() * availableDates.length);
    const selectedDate = availableDates[randomIndex];
    const dateText = selectedDate.querySelector('span').textContent.trim();

    Statistics.addLog(`🎲 随机选择日期: ${dateText}号 (${randomIndex + 1}/${availableDates.length})`);

    selectedDate.click();
    await wait(50); // 减少等待时间

    return true;

  } catch (error) {
    Statistics.addLog(`随机选择日期出错: ${error.message}`, 'error');
    return false;
  }
}

// 使用自动检测的随机选择时间
async function selectRandomTimeWithAutoDetection(pickerPanel) {
  try {
    // 检查pickerPanel是否有效
    if (!pickerPanel || typeof pickerPanel.querySelectorAll !== 'function') {
      Statistics.addLog(`❌ 日期选择器面板无效，跳过时间设置`);
      return false;
    }

    // 查找时间输入框
    const timeInputs = pickerPanel.querySelectorAll('.el-date-picker__time-header input');
    if (timeInputs.length < 2) {
      Statistics.addLog(`⚠️ 未找到时间输入框，跳过时间设置`);
      return false;
    }

    // 生成随机时间（工作时间9-17点）
    const randomHour = 9 + Math.floor(Math.random() * 9); // 9-17点
    const randomMinute = Math.floor(Math.random() * 60); // 0-59分钟

    const timeString = `${String(randomHour).padStart(2, '0')}:${String(randomMinute).padStart(2, '0')}:00`;

    Statistics.addLog(`🎲 随机选择时间: ${timeString}`);

    // 设置时间到时间输入框
    const timeInput = timeInputs[1]; // 第二个输入框是时间
    timeInput.focus();
    await wait(getOperationWaitTime()); // 使用动态等待时间

    timeInput.value = timeString;
    timeInput.dispatchEvent(new Event('input', { bubbles: true }));
    timeInput.dispatchEvent(new Event('change', { bubbles: true }));

    await wait(getOperationWaitTime()); // 使用动态等待时间

    return true;

  } catch (error) {
    Statistics.addLog(`随机选择时间出错: ${error.message}`, 'error');
    return false;
  }
}

// 随机选择时间（原版本保留）
async function selectRandomTime(pickerPanel) {
  try {
    // 查找时间输入框
    const timeInputs = pickerPanel.querySelectorAll('.el-date-picker__time-header input');
    if (timeInputs.length < 2) {
      Statistics.addLog(`⚠️ 未找到时间输入框，跳过时间设置`);
      return false;
    }

    // 生成随机时间（工作时间9-17点）
    const randomHour = 9 + Math.floor(Math.random() * 9); // 9-17点
    const randomMinute = Math.floor(Math.random() * 60); // 0-59分钟

    const timeString = `${String(randomHour).padStart(2, '0')}:${String(randomMinute).padStart(2, '0')}:00`;

    Statistics.addLog(`🎲 随机选择时间: ${timeString}`);

    // 设置时间到时间输入框
    const timeInput = timeInputs[1]; // 第二个输入框是时间
    timeInput.focus();
    await wait(50); // 减少等待时间

    timeInput.value = timeString;
    timeInput.dispatchEvent(new Event('input', { bubbles: true }));
    timeInput.dispatchEvent(new Event('change', { bubbles: true }));

    await wait(50); // 减少等待时间

    return true;

  } catch (error) {
    Statistics.addLog(`随机选择时间出错: ${error.message}`, 'error');
    return false;
  }
}

// 尝试通过"此刻"按钮设置日期
async function tryNowButton(dateInput, fieldName) {
  try {


    // 点击输入框打开日期选择器
    dateInput.click();
    await wait(getOperationWaitTime());

    // 查找"此刻"按钮
    const nowButtons = document.querySelectorAll('.el-picker-panel__footer button');

    if (nowButtons.length > 0) {
      const nowButton = Array.from(nowButtons).find(btn =>
        btn.textContent.includes('此刻') || btn.textContent.includes('现在')
      );

      if (nowButton) {
        nowButton.click();
        await wait(getOperationWaitTime());

        // 验证设置结果
        if (dateInput.value && dateInput.value.trim() !== '') {
          Statistics.addLog(`✅ ${fieldName}设置成功: ${dateInput.value}`);

          // 关闭日期选择器
          document.body.click();
          await wait(getOperationWaitTime());

          return true;
        }
      }
    }

    Statistics.addLog(`❌ ${fieldName}"此刻"按钮方法失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ ${fieldName}"此刻"按钮方法出错: ${error.message}`);
    return false;
  }
}

// 尝试通过日期选择器面板设置
async function tryDatePickerPanel(dateInput, fieldName) {
  try {
    Statistics.addLog(`📅 方法2: 尝试通过日期选择器面板设置${fieldName}`);

    // 点击输入框打开日期选择器
    dateInput.click();
    await wait(getOperationWaitTime() * 2);

    // 查找日期选择器面板
    const panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
    if (panels.length === 0) {
      Statistics.addLog(`❌ ${fieldName}日期选择器面板未打开`);
      return false;
    }

    Statistics.addLog(`✅ 找到 ${panels.length} 个日期选择器面板`);

    // 生成未来日期
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);

    // 尝试点击对应的日期
    const targetDay = futureDate.getDate();
    const dayButtons = document.querySelectorAll('.el-date-table td .cell');

    for (const dayButton of dayButtons) {
      if (dayButton.textContent.trim() === String(targetDay)) {
        Statistics.addLog(`📅 点击日期: ${targetDay}号`);
        dayButton.click();
        await wait(500);

        // 如果是日期时间选择器，还需要设置时间
        const timeInputs = document.querySelectorAll('.el-time-spinner input');
        if (timeInputs.length >= 2) {
          // 设置小时
          timeInputs[0].value = '14';
          timeInputs[0].dispatchEvent(new Event('input', { bubbles: true }));

          // 设置分钟
          timeInputs[1].value = '30';
          timeInputs[1].dispatchEvent(new Event('input', { bubbles: true }));

          await wait(300);
        }

        // 点击确定按钮
        const confirmButtons = document.querySelectorAll('.el-picker-panel__footer button');
        const confirmButton = Array.from(confirmButtons).find(btn =>
          btn.textContent.includes('确定') || btn.textContent.includes('确认')
        );

        if (confirmButton) {
          confirmButton.click();
          await wait(500);

          // 验证设置结果
          if (dateInput.value && dateInput.value.trim() !== '') {
            Statistics.addLog(`✅ ${fieldName}通过日期面板设置成功: ${dateInput.value}`);
            return true;
          }
        }

        break;
      }
    }

    Statistics.addLog(`❌ ${fieldName}日期面板方法失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ ${fieldName}日期面板方法出错: ${error.message}`);
    return false;
  }
}

// 尝试键盘输入
async function tryKeyboardInput(dateInput, fieldName) {
  try {
    Statistics.addLog(`📅 方法3: 尝试键盘输入设置${fieldName}`);

    // 生成未来日期字符串
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);
    futureDate.setHours(14);
    futureDate.setMinutes(30);

    const year = futureDate.getFullYear();
    const month = String(futureDate.getMonth() + 1).padStart(2, '0');
    const day = String(futureDate.getDate()).padStart(2, '0');
    const hour = String(futureDate.getHours()).padStart(2, '0');
    const minute = String(futureDate.getMinutes()).padStart(2, '0');
    const dateString = `${year}-${month}-${day} ${hour}:${minute}`;

    Statistics.addLog(`📅 准备键盘输入日期: ${dateString}`);

    // 聚焦输入框
    dateInput.focus();
    await wait(200);

    // 清空输入框
    dateInput.select();
    await wait(100);

    // 模拟键盘输入（添加超时保护）
    dateInput.value = '';
    let charIndex = 0;
    for (const char of dateString) {
      if (charIndex > 20) { // 防止无限循环
        Statistics.addLog(`⚠️ 键盘输入超过20个字符，停止输入`);
        break;
      }

      dateInput.value += char;
      dateInput.dispatchEvent(new KeyboardEvent('keydown', { key: char, bubbles: true }));
      dateInput.dispatchEvent(new KeyboardEvent('keypress', { key: char, bubbles: true }));
      dateInput.dispatchEvent(new Event('input', { bubbles: true }));
      dateInput.dispatchEvent(new KeyboardEvent('keyup', { key: char, bubbles: true }));
      await wait(getOperationWaitTime() / 2);
      charIndex++;
    }

    // 触发change和blur事件
    dateInput.dispatchEvent(new Event('change', { bubbles: true }));
    await wait(100);
    dateInput.blur();
    dateInput.dispatchEvent(new Event('blur', { bubbles: true }));
    await wait(500);

    // 验证设置结果
    if (dateInput.value && dateInput.value.includes(dateString.split(' ')[0])) {
      Statistics.addLog(`✅ ${fieldName}通过键盘输入设置成功: ${dateInput.value}`);
      return true;
    }

    Statistics.addLog(`❌ ${fieldName}键盘输入方法失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ ${fieldName}键盘输入方法出错: ${error.message}`);
    return false;
  }
}

// 尝试打开日期选择器
async function tryOpenDatePicker(dateEditor, dateInput, fieldName) {
  try {

    // 方法1: 点击输入框
    dateInput.click();
    await wait(getOperationWaitTime());

    let panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
    if (panels.length > 0) {
      return true;
    }

    // 方法2: 点击日期编辑器容器
    dateEditor.click();
    await wait(getOperationWaitTime());

    panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
    if (panels.length > 0) {
      return true;
    }

    // 方法3: 点击日期图标
    const dateIcon = dateEditor.querySelector('.el-input__icon, .el-icon-time, .el-icon-date');
    if (dateIcon) {
      dateIcon.click();
      await wait(getOperationWaitTime());

      panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
      if (panels.length > 0) {
        return true;
      }
    }

    // 方法4: 双击输入框
    dateInput.dispatchEvent(new MouseEvent('dblclick', { bubbles: true }));
    await wait(getOperationWaitTime());

    panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
    if (panels.length > 0) {
      return true;
    }

    Statistics.addLog(`❌ ${fieldName}所有打开方法均失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ ${fieldName}打开日期选择器出错: ${error.message}`);
    return false;
  }
}

// 尝试简单设置日期（不依赖日期选择器）
async function trySimpleDateSet(dateInput, fieldName) {
  try {
    Statistics.addLog(`📅 尝试简单设置${fieldName}（不打开选择器）`);

    // 生成未来日期
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);
    futureDate.setHours(14);
    futureDate.setMinutes(30);

    const year = futureDate.getFullYear();
    const month = String(futureDate.getMonth() + 1).padStart(2, '0');
    const day = String(futureDate.getDate()).padStart(2, '0');
    const hour = String(futureDate.getHours()).padStart(2, '0');
    const minute = String(futureDate.getMinutes()).padStart(2, '0');
    const dateString = `${year}-${month}-${day} ${hour}:${minute}`;

    Statistics.addLog(`📅 设置日期为: ${dateString}`);

    // 聚焦并清空
    dateInput.focus();
    await wait(200);
    dateInput.select();
    await wait(100);

    // 直接设置值
    dateInput.value = dateString;

    // 触发所有可能的事件
    const events = ['focus', 'input', 'change', 'blur'];
    for (const eventType of events) {
      dateInput.dispatchEvent(new Event(eventType, { bubbles: true }));
      await wait(100);
    }

    // 模拟回车键确认
    dateInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));
    dateInput.dispatchEvent(new KeyboardEvent('keyup', { key: 'Enter', bubbles: true }));

    await wait(500);

    // 验证设置结果
    if (dateInput.value && (dateInput.value === dateString || dateInput.value.includes(dateString.split(' ')[0]))) {
      Statistics.addLog(`✅ ${fieldName}简单设置成功: ${dateInput.value}`);
      return true;
    }

    Statistics.addLog(`❌ ${fieldName}简单设置失败，当前值: ${dateInput.value}`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ ${fieldName}简单设置出错: ${error.message}`);
    return false;
  }
}
