# 线索等级功能分析与修改说明

## 原有工作机制

### 1. 线索等级选项
根据ys文件分析，线索等级有三个选项：
- `H（2天内跟进）` - 高优先级
- `A（7天内跟进）` - 中优先级  
- `B（30天内跟进）` - 低优先级

### 2. 原有选择策略
代码中的原有逻辑是：
1. **优先选择A级**：优先查找包含"A"和"7天"的选项
2. **备选A级**：如果没找到，查找包含"A"的选项
3. **兜底策略**：选择第一个非空选项

### 3. 特殊处理逻辑
- 如果当前值不是"30天内跟进"，会强制修改为30天内跟进
- 这意味着原来倾向于选择B级（30天内跟进）

## 修改内容

### 1. 配置文件修改
在必填字段配置中添加了 `randomSelect: true` 标记：
```javascript
'线索等级': {
  options: ['B（30天内跟进）', 'A（7天内跟进）', 'H（2天内跟进）'],
  defaultValue: 'B（30天内跟进）',
  required: true,
  randomSelect: true  // 启用随机选择
}
```

### 2. 检测逻辑修改
修改了字段检测逻辑，支持随机选择模式：
```javascript
if (field.name === '线索等级') {
  // 如果启用随机选择，总是处理该字段
  if (field.randomSelect) {
    fieldsToProcess.push(field);
    Statistics.addLog(`🎲 线索等级启用随机选择模式`);
  } else {
    // 原有逻辑：如果不是30天就强制修改
    // ...
  }
}
```

### 3. 选择逻辑修改
在 `handleLevelSelect` 函数中添加了随机选择功能：
```javascript
// 检查是否启用随机选择（从设置中获取）
const enableRandomSelect = settings && settings.randomLevelSelect;

if (enableRandomSelect && validOptions.length > 0) {
  // 随机选择模式
  const randomIndex = Math.floor(Math.random() * validOptions.length);
  selectedOption = validOptions[randomIndex];
  Statistics.addLog(`🎲 随机选择线索等级 (${randomIndex + 1}/${validOptions.length}): ${selectedOption.textContent.trim()}`);
} else {
  // 原有逻辑：优先选择策略
  // ...
}
```

### 4. UI界面修改
在popup.html中添加了控制选项：
```html
<div class="input-row">
  <div class="input-label"></div>
  <label class="checkbox-wrapper">
    <input type="checkbox" id="randomLevelSelect">
    <span class="checkbox-text">随机选择线索等级</span>
  </label>
</div>
```

### 5. 设置保存修改
在popup.js中添加了对应的设置处理：
- `getSettings()` 函数中添加 `randomLevelSelect` 字段
- `loadSettings()` 函数中添加加载逻辑
- 添加了自动保存事件监听器

## 使用方法

1. **启用随机选择**：
   - 在扩展弹窗中勾选"随机选择线索等级"选项
   - 系统会在三个等级中随机选择：H、A、B

2. **禁用随机选择**：
   - 取消勾选该选项
   - 系统恢复原有的优先选择A级的逻辑

## 预期效果

启用随机选择后：
- 每次跟进时会随机选择线索等级
- 日志中会显示 `🎲 随机选择线索等级 (x/3): 选择的等级`
- 提高了选择的多样性，避免总是选择同一等级

## 技术细节

- 使用 `Math.floor(Math.random() * validOptions.length)` 实现真随机选择
- 保持了原有的错误处理和重试机制
- 设置会自动保存到Chrome存储中
- 兼容原有的强制填充模式
